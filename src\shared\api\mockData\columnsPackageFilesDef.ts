export const columnsPackageFilesDef = [
  {
    title: 'Имя',
    dataIndex: 'name',
    key: 'name',
    columnType: 'String',
    width: 300,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Расположение файла',
    dataIndex: 'cabinet',
    key: 'cabinet',
    columnType: 'String',
    width: 600,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Размер, кБ',
    dataIndex: 'size',
    key: 'size',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Номер заявки',
    dataIndex: 'requestNotice',
    key: 'requestNotice',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Основной',
    dataIndex: 'isMain',
    key: 'isMain',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'УКЭП',
    dataIndex: 'ukepId',
    key: 'ukepId',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Автор подписи',
    dataIndex: 'signAuthor',
    key: 'signAuthor',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'УНЭП',
    dataIndex: 'unepId',
    key: 'unepId',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата загрузки файла',
    dataIndex: 'dateUpload',
    key: 'dateUpload',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Источник',
    dataIndex: 'isOmni',
    key: 'isOmni',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Привязка файла',
    dataIndex: 'unlinkDef',
    key: 'unlinkDef',
    columnType: 'unlinkDef',
    width: 40,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Просмотр и выгрузка',
    dataIndex: 'downloadButton',
    key: 'downloadButton',
    columnType: 'downloadButton',
    width: 40,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
];
