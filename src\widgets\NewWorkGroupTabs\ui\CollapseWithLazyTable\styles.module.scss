.collapse {
  border: none;
  background: transparent;

  :global(.ant-collapse-item) {
    border: none;
    background: transparent;

    :global(.ant-collapse-header) {
      padding: 8px 16px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 8px;
    }

    :global(.ant-collapse-content) {
      border: none;
      background: transparent;

      :global(.ant-collapse-content-box) {
        padding: 0;
      }
    }
  }
}

.nestedTable {
  margin-bottom: 16px;
}

.paginationContainer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}
