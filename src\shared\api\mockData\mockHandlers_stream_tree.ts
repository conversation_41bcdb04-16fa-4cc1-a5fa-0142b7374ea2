import { http, HttpResponse } from 'msw';

export const sseHandlers = [
  http.get(
    'http://127.0.0.1:18080/filenet/publication/v2/get/as/tree/stream?folder=data&id=43234',
    async ({ request }) => {
      // 1) Заголовки SSE-потока
      const headers = {
        'Content-Type': 'text/event-stream',
        Connection: 'keep-alive',
        'Cache-Control': 'no-cache',
      };

      // 2) Стримим через ReadableStream
      const stream = new ReadableStream({
        async start(controller) {
          // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
          const push = (obj: unknown) =>
            controller.enqueue(
              new TextEncoder().encode(`data: ${JSON.stringify(obj)}\n\n`),
            );

          const docTypes = [
            'Договор',
            'Заявление',
            'Анкета',
            'Справка',
            'Выписка',
            'Отчет',
            'Акт',
            'Приложение',
            'Паспорт сделки',
          ];

          const paths = [
            'L9I28114_5/Материалы/13001/ЮЛ/6112912380/Документы по кредиту',
            'L9I28114_5/Документы/13001/ЮЛ/6112912380/Кредитное досье',
            'L9I28114_5/Переписка/13001/ЮЛ/6112912380/Документы по залогу',
            'L9I28114_5/Отчетность/13001/ЮЛ/6112912380/Переписка',
            'L9I28114_5/Отчетность/13001/ЮЛ/6112912380/Отчетность',
            'L9I28114_5/Решения КК/13001/ЮЛ/6112912380/Решения КК',
          ];

          const getRandomFileName = (
            id: number,
          ): { name: string; path: string } => {
            const type = docTypes[Math.floor(Math.random() * docTypes.length)];
            const path = paths[Math.floor(Math.random() * paths.length)];
            const date = new Date(
              2023,
              Math.floor(Math.random() * 12),
              Math.floor(Math.random() * 28) + 1,
            )
              .toLocaleDateString('ru-RU')
              .replace(/\./g, '');
            return {
              name: `${type}_${date}_${id}`,
              path,
            };
          };

          let fileId = 1;
          while (fileId < 200000) {
            // eslint-disable-next-line no-await-in-loop
            // await delay(10);
            const file = getRandomFileName(fileId);
            push({
              id: fileId,
              title: `${fileId}${file.path}/${file.name}.pdf`,
              contentPath: `${fileId}2023/9I/L9I28114/${file.path}/${file.name}.pdf`,
              author: null,
              fileSize: '1 Байт',
              documentTypeName: null,
              documentTypeExternalId: null,
              groupDocumentTypeName: null,
              groupDocumentTypeExternalId: null,
              inspectedOrganizationUnits: [999770, 983402, 988838],
              associatedDocuments: [],
              publishedDatetime: '17.02.2025 16:50:11',
              modifiedDatetime: '17.02.2025 16:50:11',
              uploadDatetime: '17.02.2025 16:50:11',
              uploadGibrDatetime: null,
              modifiedGibrDatetime: null,
              canceled: false,
              inspectedOrganizationUnitsData: [],
              inspectorOrganizationUnitsData: [],
              auditCardId: null,
              auditCardName: null,
              auditPeriodFrom: '28.11.2023',
              auditPeriodTo: '31.12.9999',
              attachmentId: 70927,
              type: 'INDEX_CONTENT',
              typeName: 'Содержимое индексной папки',
              folder: '2023/9I/L9I28114/',
              hasAnnexFiles: null,
              viewDownloadPermissions: ['DOWNLOAD', 'VIEW'],
              hasMoreChildren: false,
              p362Id: 303146890,
              auditQuestionIds: [
                8781989, 8782335, 8782416, 8781991, 8781996, 8781997, 8781998,
                8781999, 8782001, 8782002, 8782006, 8782007, 8782009, 8782011,
                8782012, 8782338, 8782014, 8782015, 8782344, 8782021, 8782024,
                8782346, 8782348, 8782036, 8782349, 8782037, 8782427, 8782428,
                8782429, 8782038, 8782630, 8782631, 8782651, 8782632, 8782633,
                8782044, 8782045, 8782046, 8782353, 8782375, 8782518, 8782354,
                8782376, 8782356, 8782435, 149008672, 8782436, 8782437, 8782438,
                149149609, 8782048, 8782049, 8782050, 8782052, 8782359, 8782380,
                8782440, 8782522, 8782361, 8782055,
              ],
              auditPeriod: {
                '2023-11-28T00:00:00.000+03:00': 253402289999000,
              },
              auditCard: false,
              label: `${fileId}_${file.path}/${file.name}.pdf`,
              main: true,
              document: true,
              iconType: 'pdf',
            });
            fileId += 1;
          }
          push({ id: -1 });
          // controller.close(); // Бесконечный поток, не закрываем контроллер
        },
      });

      return new HttpResponse(stream, { status: 200, headers });
    },
  ),
];

// export const fileNethandlers = [
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/get/45555`,
//     async () => {
//       await delay();
//       return HttpResponse.json(mockDocument, { status: 200 });
//     },
//   ),
// ];

export const handlers = [
  /* ...fileNethandlers */
  ...sseHandlers,
];
