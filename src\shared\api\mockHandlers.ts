/* eslint-disable import/no-internal-modules */
import { http, HttpResponse } from 'msw';
import { delay } from 'shared/lib/delay';

import {
  packageDefTableData,
  packageFilesData,
  packageFilesColumns,
  requestItemsData,
  requestItemsColumns,
} from './mockData';
import {
  eppTreeMockData,
  krgTreeRootMockData,
  reestry,
  resultaty,
} from './mockData/eppTreeMockData';
import { krg3RequestNoticeMockData } from './mockData/krg3RequestNoticeMockData';
import { krgTableMockData } from './mockData/krgTableMockData';
import { krg3NoticeForRequestMockData } from './mockData/notices';

// const fileNethandlers = [
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/get/11743`,
//     async () => {
//       await delay();
//       return HttpResponse.json(fileCard, { status: 200 });
//     },
//   ),
//   http.get(
//     `${devServerUrl}${FILENET_SERVICE}/publication/v2/get/as/tree`,
//     async () => {
//       await delay(3000);
//       return HttpResponse.json(fileTree, { status: 200 });
//     },
//   ),
//   http.post(
//     `${devServerUrl}${FILENET_SERVICE}/main/find/documents`,
//     async () => {
//       await delay();
//       return HttpResponse.json(filesList, { status: 200 });
//     },
//   ),
// ];

const mockHandlers = [
  http.post('http://127.0.0.1:18080/kzid_rest/krg3', async ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get('page') || '1';
    const size = url.searchParams.get('size') || '10';

    await delay();
    return HttpResponse.json(
      {
        ...krgTableMockData,
        // You can use page and size here if needed for pagination
        page: parseInt(page, 10),
        size: parseInt(size, 10),
      },
      { status: 200 },
    );
  }),

  // // Мок для основной таблицы packageDef с ленивыми табами
  // http.post(
  //   'http://127.0.0.1:18080/kzid_rest/krg3_input_package_def',
  //   async ({ request }) => {
  //     const url = new URL(request.url);
  //     const pageNumber = url.searchParams.get('pageNumber') || '1';
  //     const pageSize = url.searchParams.get('pageSize') || '10';

  //     await delay(500);
  //     return HttpResponse.json(
  //       {
  //         ...packageDefTableData,
  //         pagination: {
  //           total: 3,
  //           pageSize: parseInt(pageSize, 10),
  //         },
  //         pageNumber: parseInt(pageNumber, 10),
  //       },
  //       { status: 200 },
  //     );
  //   },
  // ),

  // // Мок для загрузки содержимого таба с пагинацией
  // http.post(
  //   'http://127.0.0.1:18080/kzid_rest/krg3_package_files_def',
  //   async ({ request }) => {
  //     const url = new URL(request.url);
  //     const pageNumber = parseInt(
  //       url.searchParams.get('pageNumber') || '1',
  //       10,
  //     );
  //     const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

  //     await delay(800); // Имитируем загрузку

  //     const startIndex = (pageNumber - 1) * pageSize;
  //     const endIndex = startIndex + pageSize;
  //     const pageFiles = packageFilesData.slice(startIndex, endIndex);

  //     return HttpResponse.json(
  //       {
  //         columns: packageFilesColumns,
  //         rows: pageFiles,
  //         pagination: {
  //           total: packageFilesData.length,
  //           pageSize,
  //         },
  //         pageNumber,
  //         sort: null,
  //         group: null,
  //       },
  //       { status: 200 },
  //     );
  //   },
  // ),

  // // Мок для основной таблицы request_notice с ленивыми табами
  // http.post(
  //   'http://127.0.0.1:18080/kzid_rest/krg3_request_notice',
  //   async ({ request }) => {
  //     const url = new URL(request.url);
  //     const pageNumber = url.searchParams.get('pageNumber') || '1';
  //     const pageSize = url.searchParams.get('pageSize') || '10';

  //     return HttpResponse.json(
  //       krg3RequestNoticeMockData(pageNumber, pageSize),
  //       { status: 200 },
  //     );
  //   },
  // ),

  // // Мок для загрузки содержимого таба "Пункты заявки"
  // http.post(
  //   'http://127.0.0.1:18080/kzid_rest/krg3_request_item',
  //   async ({ request }) => {
  //     const url = new URL(request.url);
  //     const pageNumber = parseInt(
  //       url.searchParams.get('pageNumber') || '1',
  //       10,
  //     );
  //     const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

  //     await delay(600); // Имитируем загрузку

  //     const startIndex = (pageNumber - 1) * pageSize;
  //     const endIndex = startIndex + pageSize;
  //     const pageItems = requestItemsData.slice(startIndex, endIndex);

  //     return HttpResponse.json(
  //       {
  //         columns: requestItemsColumns,
  //         rows: pageItems,
  //         pagination: {
  //           total: requestItemsData.length,
  //           pageSize,
  //         },
  //         pageNumber,
  //         sort: null,
  //         group: null,
  //       },
  //       { status: 200 },
  //     );
  //   },
  // ),

  // // Мок для загрузки содержимого таба "Уведомления, связанные с заявкой"
  // http.post(
  //   'http://127.0.0.1:18080/kzid_rest/krg3_notice_for_request',
  //   async ({ request }) => {
  //     const url = new URL(request.url);
  //     const pageNumber = parseInt(
  //       url.searchParams.get('pageNumber') || '1',
  //       10,
  //     );
  //     const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

  //     await delay(700); // Имитируем загрузку

  //     const mockData = krg3NoticeForRequestMockData(
  //       Number(pageNumber),
  //       pageSize,
  //     );
  //     console.log('krg3_notice_for_request mock data:', {
  //       pageNumber,
  //       pageSize,
  //       totalRows: mockData.rows.length,
  //       totalInPagination: mockData.pagination.total,
  //       paginationPageSize: mockData.pagination.pageSize,
  //     });

  //     return HttpResponse.json(mockData, { status: 200 });
  //   },
  // ),
];

const time = 1000;

const eppTreeMockHandlers = [
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const pos = url.searchParams.get('pos');
      const cabinetId = url.searchParams.get('cabinetId');
      const user = url.searchParams.get('user');
      const isCabinet = url.searchParams.get('isCabinet') === 'true';

      // Only mock requests with specific parameters
      if (
        cabinetId === 'bd44a562-fcae-45b6-b38f-539e7b69ade9' &&
        user === 'admin' &&
        isCabinet === true
      ) {
        await delay(time);
        return HttpResponse.json(
          {
            ...krgTreeRootMockData,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '4109a8e1-7b82-44b5-b33a-ee978c425517') {
        await delay(time);
        return HttpResponse.json(
          {
            ...eppTreeMockData,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '389c9fe9-b118-4773-8e2a-327a183a4763') {
        await delay(time);
        return HttpResponse.json(
          {
            ...reestry,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '') {
        await delay(time);
        return HttpResponse.json(
          {
            ...reestry,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      if (itemId === '1af7bc7c-57e7-4d69-992b-16f635a0fe02') {
        await delay(time);
        return HttpResponse.json(
          {
            ...resultaty,
            // Include the query parameters in the response if needed
            requestParams: { itemId, pos, cabinetId, user, isCabinet },
          },
          { status: 200 },
        );
      }

      // For requests with different parameters, don't return a response
      // This will cause MSW to pass through to the real API
      return undefined;
    },
  ),

  http.put(
    'http://127.0.0.1:18080/kzid_rest/krg3_file_data/visibility',
    async () => {
      await delay(0);
      return new HttpResponse(null, { status: 204 });
    },
  ),
];

export const handlers = [...mockHandlers, ...eppTreeMockHandlers];
