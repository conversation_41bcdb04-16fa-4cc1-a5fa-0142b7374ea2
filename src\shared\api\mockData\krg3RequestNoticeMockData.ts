export const krg3RequestNoticeMockData = (
  pageSize: string,
  pageNumber: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any => ({
  columns: [
    {
      title: 'Номер',
      dataIndex: 'number',
      key: 'number',
      columnType: 'String',
      width: 100,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Вид',
      dataIndex: 'type',
      key: 'type',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: 'default',
      filters: [
        {
          key: '1',
          value: '1',
          text: 'Заявка',
        },
        {
          key: '2',
          value: '2',
          text: 'Уведомление',
        },
      ],
      sortable: false,
    },
    {
      title: 'Статус',
      dataIndex: 'status',
      key: 'status',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: 'default',
      filters: [
        {
          key: '0',
          value: 'edaceb01-f2a9-3d29-e053-030011ace591',
          text: 'В работе',
        },
        {
          key: '1',
          value: 'ea5fa154-3e20-a88d-e053-040011ac4cc6',
          text: 'Готово к отправке',
        },
      ],
      sortable: false,
    },
  ],
  rows: [
    {
      dateReg: null,
      note: null,
      dateSigning: null,
      dateRelease: null,
      subject: null,
      directoryOutputId: null,
      dateLastChange: '16.11.2022 13:14:02',
      questions: '',
      regNumSigned: null,
      type: 'Заявка',
      dateCreate: '16.11.2022 13:14:02',
      dateSend: '15.12.2022 16:40:19',
      number: '1',
      userCreate: 'Кирюхин Евгений Валерьевич',
      regNum: null,
      key: '309fc7da-1703-4067-a455-d9199ab8e94c',
      dateReception: null,
      actual: 'Актуально',
      directoryInputId: null,
      inn: null,
      nestedTable: {
        tabs: [
          {
            label: 'Пункты заявки',
            key: '1',
            endpoint: 'krg3_request_item',
            tableData: null,
          },
          {
            label: 'Уведомления, связанные с заявкой',
            key: '4',
            endpoint: 'krg3_notice_for_request',
            tableData: null,
          },
        ],
      },
      rowId: {
        hasOutputFiles: false,
        isPrepared: true,
        isActual: true,
        additional: '№ 1, не привязанные к пунктам',
        fileLinks: [],
        hasNested: true,
        id: '309fc7da-1703-4067-a455-d9199ab8e94c',
        type: '1',
        haveMainFile: 0,
        hasInputFiles: false,
        isDifferentialAccessEnabled: false,
        isUpdatableStatus: false,
      },
      accessStamp: 'ДСП-инспектирование',
      requestInternalEdit: '',
      statusId: 'request_prepared',
      hint: {
        status: 'Готово к отправке',
      },
      status: 'Готово к отправке',
    },
  ],
  pagination: {
    total: 9,
    pageSize: parseInt(pageSize, 10),
  },
  pageNumber: parseInt(pageNumber, 10),
  sort: null,
  group: null,
});
