// Моковые данные для packageDef (ленивые табы)

export const packageDefTableData = {
  columns: [
    {
      title: 'Номер',
      dataIndex: 'number',
      key: 'number',
      columnType: 'String',
      width: 100,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Статус',
      dataIndex: 'status',
      key: 'status',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Дата регистрации или отказа в САДД',
      dataIndex: 'dateRegOrDissSadd',
      key: 'dateRegOrDissSadd',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
  ],
  rows: [
    {
      number: 1,
      status: 'Принят в КРГ',
      dateRegOrDissSadd: '15.10.2022 11:22:10',
      dateUploadRegPackage: '16.11.2022 18:28:07',
      dateLastChange: '16.11.2022 18:28:07',
      requests: '',
      id: '701d1479-05d5-4f61-a13d-de3acce42ce3',
      dateSendRegPackage: '16.10.2022 11:22:10',
      nestedTable: {
        tabs: [
          {
            label: 'Файлы в составе пакета',
            key: '1',
            endpoint: 'krg3_package_files_def',
            tableData: null,
          },
        ],
      },
      key: '701d1479-05d5-4f61-a13d-de3acce42ce3',
      rowId: {
        id: '701d1479-05d5-4f61-a13d-de3acce42ce3',
        hasNested: true,
      },
    },
    {
      number: 2,
      status: 'В обработке',
      dateRegOrDissSadd: '20.10.2022 14:30:15',
      dateUploadRegPackage: '21.11.2022 10:15:30',
      dateLastChange: '21.11.2022 10:15:30',
      requests: '',
      id: '802e2580-16e6-5f72-b24e-ef4bddf53df4',
      dateSendRegPackage: '21.10.2022 14:30:15',
      nestedTable: {
        tabs: [
          {
            label: 'Файлы в составе пакета',
            key: '1',
            endpoint: 'krg3_package_files_def',
            tableData: null,
          },
        ],
      },
      key: '802e2580-16e6-5f72-b24e-ef4bddf53df4',
      rowId: {
        id: '802e2580-16e6-5f72-b24e-ef4bddf53df4',
        hasNested: true,
      },
    },
  ],
};

export const packageFilesData = [
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '19.12.2022 15:08:54',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'rar',
      isFileSignExist: false,
      canDownload: true,
      id: 'a16247e8-5180-41d1-a014-9fbd25862aab',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 68.95,
    name: 'xsd.rar',
    unepId: null,
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'a16247e8-5180-41d1-a014-9fbd25862aab',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '19.12.2022 15:01:43',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'txt',
      isFileSignExist: false,
      canDownload: true,
      id: 'a6b43ab7-1c47-44ad-ba44-dcddaa553609',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 8.94,
    name: 'test.txt',
    unepId: null,
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'a6b43ab7-1c47-44ad-ba44-dcddaa553609',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 1,
    ukepId: '12345',
    unlinkDef: '',
    dateUpload: '20.12.2022 10:15:30',
    signAuthor: 'Иванов И.И.',
    rowId: {
      canView: true,
      format: 'pdf',
      isFileSignExist: true,
      canDownload: true,
      id: 'b7c54bc8-2d58-45be-bb55-ecdebb664710',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 125.67,
    name: 'document.pdf',
    unepId: '67890',
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'b7c54bc8-2d58-45be-bb55-ecdebb664710',
  },
  {
    downloadButton: '',
    isOmni: 'САДД',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '21.12.2022 14:22:15',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'docx',
      isFileSignExist: false,
      canDownload: true,
      id: 'c8d65cd9-3e69-56cf-cc66-fdefc775821',
      isRemovable: true,
      enableUnlinkDef: true,
    },
    size: 45.23,
    name: 'report.docx',
    unepId: null,
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'c8d65cd9-3e69-56cf-cc66-fdefc775821',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '22.12.2022 09:45:12',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'xlsx',
      isFileSignExist: false,
      canDownload: true,
      id: 'd9e76dea-4f7a-67d0-dd77-0ef0d886932',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 78.12,
    name: 'data.xlsx',
    unepId: null,
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'd9e76dea-4f7a-67d0-dd77-0ef0d886932',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '22.12.2022 09:45:12',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'xlsx',
      isFileSignExist: false,
      canDownload: true,
      id: 'd9e76dea-4f7a-67d0-dd77-0ef0d886934',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 78.12,
    name: 'data.xlsx',
    unepId: null,
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'd9e76dea-4f7a-67d0-dd77-0ef0d886934',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '22.12.2022 09:45:12',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'xlsx',
      isFileSignExist: false,
      canDownload: true,
      id: 'd9e76dea-4f7a-67d0-dd77-0ef0d886935',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 78.12,
    name: 'data.xlsx',
    unepId: null,
    cabinet: '/Пакет 2',
    requestNotice: '',
    key: 'd9e76dea-4f7a-67d0-dd77-0ef0d886935',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 1,
    ukepId: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    unlinkDef: '',
    dateUpload: '15.11.2023 14:30:05',
    signAuthor: 'Иванов И.И.',
    rowId: {
      canView: true,
      format: 'pdf',
      isFileSignExist: true,
      canDownload: true,
      id: '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
      isRemovable: true,
      enableUnlinkDef: false,
    },
    size: 1240.5,
    name: 'Договор_123.pdf',
    unepId: null,
    cabinet: '/Договоры/2023',
    requestNotice: 'Требует согласования',
    key: '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
  },
  {
    downloadButton: '',
    isOmni: null,
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '02.03.2024 11:05:19',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'docx',
      isFileSignExist: false,
      canDownload: true,
      id: 'f0e9d8c7-b6a5-4321-fedc-ba9876543210',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 45.8,
    name: 'Заявление на отпуск.docx',
    unepId: null,
    cabinet: '/Кадровые документы',
    requestNotice: '',
    key: 'f0e9d8c7-b6a5-4321-fedc-ba9876543210',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '28.02.2024 18:22:41',
    signAuthor: 'Петрова А.С.',
    rowId: {
      canView: true,
      format: 'zip',
      isFileSignExist: false,
      canDownload: true,
      id: 'abababab-cdcd-efef-1212-343434343434',
      isRemovable: true,
      enableUnlinkDef: true,
    },
    size: 5834.12,
    name: 'Архив_проекта_X.zip',
    unepId: 'b1c2d3e4-f5a6-b7c8-d9e0-f1a2b3c4d5e6',
    cabinet: '/Проекты/Архив',
    requestNotice: '',
    key: 'abababab-cdcd-efef-1212-343434343434',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '10.01.2023 09:00:00',
    signAuthor: null,
    rowId: {
      canView: false,
      format: 'jpg',
      isFileSignExist: false,
      canDownload: true,
      id: '98765432-10fe-dcba-9876-543210fedcba',
      isRemovable: false,
      enableUnlinkDef: false,
    },
    size: 812.33,
    name: 'Скан_паспорта.jpg',
    unepId: null,
    cabinet: '/Личные документы',
    requestNotice: 'Конфиденциально',
    key: '98765432-10fe-dcba-9876-543210fedcba',
  },
  {
    downloadButton: '',
    isOmni: null,
    isMain: 1,
    ukepId: '11223344-5566-7788-99aa-bbccddeeff00',
    unlinkDef: '',
    dateUpload: '19.07.2023 16:45:12',
    signAuthor: 'Сидоров В.П.',
    rowId: {
      canView: true,
      format: 'xlsx',
      isFileSignExist: true,
      canDownload: true,
      id: 'a0b9c8d7-e6f5-a4b3-c2d1-e0f9a8b7c6d5',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 256.0,
    name: 'Финансовый_отчет_Q3.xlsx',
    unepId: null,
    cabinet: '/Финансы/Отчеты',
    requestNotice: '',
    key: 'a0b9c8d7-e6f5-a4b3-c2d1-e0f9a8b7c6d5',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '01.06.2022 10:10:10',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'png',
      isFileSignExist: false,
      canDownload: false,
      id: 'ffeeddcc-bbaa-9988-7766-554433221100',
      isRemovable: false,
      enableUnlinkDef: false,
    },
    size: 305.7,
    name: 'logo_company.png',
    unepId: null,
    cabinet: '/Маркетинг/Брендбук',
    requestNotice: '',
    key: 'ffeeddcc-bbaa-9988-7766-554433221100',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 0,
    ukepId: 'deadbeef-dead-beef-dead-beefdeadbeef',
    unlinkDef: '',
    dateUpload: '20.09.2023 12:12:12',
    signAuthor: 'Козлов Д.А.',
    rowId: {
      canView: true,
      format: 'pdf',
      isFileSignExist: true,
      canDownload: true,
      id: 'badc0ffe-e1d2-c3b4-a596-87d8e9f0a1b2',
      isRemovable: true,
      enableUnlinkDef: true,
    },
    size: 2048.0,
    name: 'Техническое_задание.pdf',
    unepId: 'c0ffeeba-ddec-0dd0-deaf-babe01234567',
    cabinet: '/Разработка/Спецификации',
    requestNotice: '',
    key: 'badc0ffe-e1d2-c3b4-a596-87d8e9f0a1b2',
  },
  {
    downloadButton: '',
    isOmni: null,
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '05.12.2022 17:55:01',
    signAuthor: null,
    rowId: {
      canView: true,
      format: 'txt',
      isFileSignExist: false,
      canDownload: true,
      id: '12345678-90ab-cdef-1234-567890abcdef',
      isRemovable: true,
      enableUnlinkDef: true,
    },
    size: 5.2,
    name: 'notes.txt',
    unepId: null,
    cabinet: '/Разное',
    requestNotice: '',
    key: '12345678-90ab-cdef-1234-567890abcdef',
  },
  {
    downloadButton: '',
    isOmni: 'ОМНИ',
    isMain: 1,
    ukepId: '7b8c9d0e-f1a2-b3c4-d5e6-f7a8b9c0d1e2',
    unlinkDef: '',
    dateUpload: '31.10.2023 23:59:59',
    signAuthor: 'Михайлов М.М.',
    rowId: {
      canView: true,
      format: 'pptx',
      isFileSignExist: true,
      canDownload: true,
      id: 'e2d1c0b9-a8f7-e6d5-c4b3-a2f1e0d9c8b7',
      isRemovable: false,
      enableUnlinkDef: false,
    },
    size: 15360.5,
    name: 'Презентация_итоги_года.pptx',
    unepId: null,
    cabinet: '/Презентации',
    requestNotice: 'Финальная версия',
    key: 'e2d1c0b9-a8f7-e6d5-c4b3-a2f1e0d9c8b7',
  },
  {
    downloadButton: '',
    isOmni: null,
    isMain: 0,
    ukepId: null,
    unlinkDef: '',
    dateUpload: '14.02.2024 08:30:00',
    signAuthor: 'Администратор',
    rowId: {
      canView: true,
      format: 'csv',
      isFileSignExist: false,
      canDownload: true,
      id: 'cacefeed-babe-feed-babe-feedcafebabe',
      isRemovable: false,
      enableUnlinkDef: true,
    },
    size: 987.6,
    name: 'user_data_export.csv',
    unepId: null,
    cabinet: '/Системные/Экспорт',
    requestNotice: '',
    key: 'cacefeed-babe-feed-babe-feedcafebabe',
  },
];

export const packageFilesColumns = [
  {
    title: 'Имя',
    dataIndex: 'name',
    key: 'name',
    columnType: 'String',
    width: 300,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Расположение файла',
    dataIndex: 'cabinet',
    key: 'cabinet',
    columnType: 'String',
    width: 600,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Размер, кБ',
    dataIndex: 'size',
    key: 'size',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Номер заявки',
    dataIndex: 'requestNotice',
    key: 'requestNotice',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Основной',
    dataIndex: 'isMain',
    key: 'isMain',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'УКЭП',
    dataIndex: 'ukepId',
    key: 'ukepId',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Автор подписи',
    dataIndex: 'signAuthor',
    key: 'signAuthor',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'УНЭП',
    dataIndex: 'unepId',
    key: 'unepId',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Дата загрузки файла',
    dataIndex: 'dateUpload',
    key: 'dateUpload',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Источник',
    dataIndex: 'isOmni',
    key: 'isOmni',
    columnType: 'String',
    width: 200,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Привязка файла',
    dataIndex: 'unlinkDef',
    key: 'unlinkDef',
    columnType: 'unlinkDef',
    width: 40,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
  {
    title: 'Просмотр и выгрузка',
    dataIndex: 'downloadButton',
    key: 'downloadButton',
    columnType: 'downloadButton',
    width: 40,
    align: 'left',
    columnUuid: null,
    filterType: null,
    filters: null,
    sortable: false,
  },
];
