import { Collapse, Pagination } from 'antd';
import { ReactNode, useState, FC } from 'react';
import {
  CollapseWithLazyTableProps,
  NWGLib,
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
} from 'widgets/NewWorkGroupTabs';
import {
  DataGrid,
  TableRowData,
  TabsWithLazyTable,
  NestedTabsWithLazyTable,
} from 'features/DataGrid';

import { ApiContainer } from 'shared/ui';
import styles from './styles.module.scss';

interface CollapseWithLazyTableComponentProps {
  activeEndpoint: Endpoint;
  customRowRender: CustomRowRender;
  getLazyTabContent: (
    record: TableRowData,
    tabKey: string,
    page?: number,
  ) => Promise<void>;
  handleRow: HandleRow;
  nestedContent: NestedTabsWithLazyTable;
  parentRow: TableRowData;
  permissions: Permissions;
  refetch: Callback;
  togglePopup: TogglePopup;
  isRequest?: boolean;
}

const CollapseWithLazyTableComponent: FC<
  CollapseWithLazyTableComponentProps
> = ({
  nestedContent,
  customRowRender,
  parentRow,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  getLazyTabContent,
  isRequest,
}) => {
  const { renderCollapseTitle, customColumnsWidth } = NWGLib;
  const [activeTabKeys, setActiveTabKeys] = useState<string[]>([]);

  const handleTabToggle = async (key: string | string[]): Promise<void> => {
    const keys = Array.isArray(key) ? key : [key];
    setActiveTabKeys(keys);

    // Загружаем содержимое для новых открытых табов
    const loadPromises = keys
      .filter((tabKey) => !activeTabKeys.includes(tabKey))
      .map(async (tabKey) => {
        const tab = nestedContent.tabs.find((t) => String(t.key) === tabKey);
        if (tab && !tab.tableData && !tab.tabStatus?.isLoading) {
          return getLazyTabContent(parentRow, tabKey);
        }
        return Promise.resolve();
      });

    await Promise.all(loadPromises);
  };

  const handlePageChange = async (
    tab: TabsWithLazyTable,
    page: number,
  ): Promise<void> => {
    await getLazyTabContent(parentRow, String(tab.key), page);
  };

  return (
    <ApiContainer
      error={parentRow.nestedTableStatus?.error as AppError}
      isPending={!!parentRow.nestedTableStatus?.isLoading}
    >
      <Collapse
        className={styles.collapse}
        activeKey={activeTabKeys}
        onChange={handleTabToggle}
      >
        {nestedContent.tabs.map((tab) => (
          <Collapse.Panel
            key={tab.key}
            header={renderCollapseTitle(
              tab.label,
              parentRow.rowId?.additional || '',
            )}
          >
            <ApiContainer
              error={tab.tabStatus?.error as AppError}
              isPending={!!tab.tabStatus?.isLoading}
            >
              {tab.tableData && (
                <>
                  <DataGrid
                    resizableProps={{ isActive: true }}
                    additionalClassNames={{
                      table: styles.nestedTable,
                      container: styles.nestedTable,
                    }}
                    columns={
                      Array.isArray(tab.tableData.columns)
                        ? tab.tableData.columns.map((column) => ({
                            ...column,
                            ...(column.columnType !== 'String' && {
                              fixed: 'right',
                              width: customColumnsWidth(
                                column?.columnType || '',
                              ),
                              align: 'center',
                              hideSorter: true,
                              hideColumnSearch: true,
                            }),
                            render: (text: string, row: TableRowData) =>
                              customRowRender(
                                text,
                                row,
                                column,
                                refetch,
                                togglePopup,
                                handleRow,
                                activeEndpoint,
                                permissions,
                                isRequest,
                              ),
                          }))
                        : []
                    }
                    rows={
                      Array.isArray(tab.tableData.rows)
                        ? tab.tableData.rows
                        : []
                    }
                    tableAdditionProps={{
                      size: 'small',
                      scroll: { x: '100%', y: '100%' },
                      pagination: false, // Отключаем встроенную пагинацию
                    }}
                  />
                  {tab.pagination &&
                    tab.pagination.total > tab.pagination.pageSize && (
                      <div className={styles.paginationContainer}>
                        <Pagination
                          current={tab.pagination.currentPage}
                          total={tab.pagination.total}
                          pageSize={tab.pagination.pageSize}
                          showSizeChanger={false}
                          onChange={(page) => handlePageChange(tab, page)}
                          size="small"
                        />
                      </div>
                    )}
                </>
              )}
            </ApiContainer>
          </Collapse.Panel>
        ))}
      </Collapse>
    </ApiContainer>
  );
};

export const collapseWithLazyTable: CollapseWithLazyTableProps = (
  nestedContent,
  customRowRender,
  parentRow,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  getLazyTabContent,
  isRequest,
): ReactNode => (
  <CollapseWithLazyTableComponent
    activeEndpoint={activeEndpoint}
    customRowRender={customRowRender}
    handleRow={handleRow}
    nestedContent={nestedContent}
    parentRow={parentRow}
    permissions={permissions}
    refetch={refetch}
    togglePopup={togglePopup}
    getLazyTabContent={getLazyTabContent}
    isRequest={isRequest}
  />
);
