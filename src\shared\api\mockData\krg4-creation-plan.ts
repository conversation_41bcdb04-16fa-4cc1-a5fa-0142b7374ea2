export const krg4CreationPlanMockData = {
  columns: [
    {
      title: '',
      dataIndex: 'COLUMN_0',
      key: 'COLUMN_0',
      columnType: 'row_checkbox',
      width: 80,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: '№',
      dataIndex: 'COLUMN_1',
      key: 'COLUMN_1',
      columnType: 'String',
      width: 80,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'ИНН ПЛ',
      dataIndex: 'COLUMN_2',
      key: 'COLUMN_2',
      columnType: 'String',
      width: 150,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Регистрационный номер и код филиала ПЛ',
      dataIndex: 'COLUMN_3',
      key: 'COLUMN_3',
      columnType: 'String',
      width: 120,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Наименование ПЛ',
      dataIndex: 'COLUMN_4',
      key: 'COLUMN_4',
      columnType: 'String',
      width: 250,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Месяц начала проверки',
      dataIndex: 'COLUMN_5',
      key: 'COLUMN_5',
      columnType: 'String',
      width: 150,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Планируемая дата начала проверки',
      dataIndex: 'COLUMN_6',
      key: 'COLUMN_6',
      columnType: 'String',
      width: 200,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Срок создания КРГ',
      dataIndex: 'COLUMN_7',
      key: 'COLUMN_7',
      columnType: 'String',
      width: 190,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Дата/время создания КРГ (обязательно)',
      dataIndex: 'COLUMN_8',
      key: 'COLUMN_8',
      columnType: 'date_picker',
      width: 250,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Текстовое описание КРГ (обязательно)',
      dataIndex: 'COLUMN_9',
      key: 'COLUMN_9',
      columnType: 'input',
      width: 150,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Основание создания КРГ (опционально)',
      dataIndex: 'COLUMN_10',
      key: 'COLUMN_10',
      columnType: 'input',
      width: 150,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Комментарий (опционально)',
      dataIndex: 'COLUMN_11',
      key: 'COLUMN_11',
      columnType: 'input',
      width: 150,
      align: 'left',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Режим активации КРГ (обязательно, по умолчанию – «вручную»)',
      dataIndex: 'COLUMN_12',
      key: 'COLUMN_12',
      columnType: 'switch',
      width: 150,
      align: 'center',
      columnUuid: null,
      filterType: null,
      filters: null,
      sortable: false,
    },
  ],
  rows: [
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 23046776,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-058a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6316103050',
      COLUMN_1: '1',
      COLUMN_4:
        'Акционерное Общество Микрофинансовая компания "Микро Капитал" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '1',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19714847,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-055b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6612010782',
      COLUMN_1: '2',
      COLUMN_4: 'ПАО "МЕТКОМБАНК"',
      COLUMN_3: '2443',
      COLUMN_0: '',
      key: '2',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720562,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-056e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7730067441',
      COLUMN_1: '3',
      COLUMN_4: 'МКИБ "РОССИТА-БАНК" ООО',
      COLUMN_3: '3257',
      COLUMN_0: '',
      key: '3',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3031036,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04d8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3006000387',
      COLUMN_1: '4',
      COLUMN_4: 'АО Банк "ККБ"',
      COLUMN_3: '1087/1',
      COLUMN_0: '',
      key: '4',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3306371,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703810717',
      COLUMN_1: '5',
      COLUMN_4: 'Общество с ограниченной ответственностью "БЭСТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '5',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22734853,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-054e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7718105676',
      COLUMN_1: '6',
      COLUMN_4: 'ПАО "РГС Банк"',
      COLUMN_3: '3073',
      COLUMN_0: '',
      key: '6',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2955101,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04b7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706028882',
      COLUMN_1: '7',
      COLUMN_4: 'АКБ "ТЕНДЕР-БАНК" (АО)',
      COLUMN_3: '2252',
      COLUMN_0: '',
      key: '7',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439534,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0513-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3019003674',
      COLUMN_1: '8',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ЛОТОС" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '8',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 3090777,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04d3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7725038220',
      COLUMN_1: '9',
      COLUMN_4: 'Банк Глобус (АО)',
      COLUMN_3: '2438',
      COLUMN_0: '',
      key: '9',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17739840,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0510-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7100001642',
      COLUMN_1: '10',
      COLUMN_4: 'ПАО "Спиритбанк"',
      COLUMN_3: '2053',
      COLUMN_0: '',
      key: '10',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3004329,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04ab-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706006720',
      COLUMN_1: '11',
      COLUMN_4: '"Банк Кремлевский" ООО',
      COLUMN_3: '2905',
      COLUMN_0: '',
      key: '11',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937159,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04a1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4826104032',
      COLUMN_1: '12',
      COLUMN_4:
        'Акционерное общество "Негосударственный пенсионный фонд "Социальное развитие"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '12',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3039591,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04df-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2310147871',
      COLUMN_1: '13',
      COLUMN_4: 'СТРАХОВОЕ АКЦИОНЕРНОЕ ОБЩЕСТВО "КРИСТАЛЛ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '13',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439210,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-053e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7709927260',
      COLUMN_1: '14',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СОСЬЕТЕ ЖЕНЕРАЛЬ СТРАХОВАНИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '14',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 9876099,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04fd-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710295979',
      COLUMN_1: '15',
      COLUMN_4: 'АО "КОММЕРЦБАНК (ЕВРАЗИЯ)"',
      COLUMN_3: '3333',
      COLUMN_0: '',
      key: '15',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17439626,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-052a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1657144410',
      COLUMN_1: '16',
      COLUMN_4: 'ЖИЛИЩНЫЙ НАКОПИТЕЛЬНЫЙ КООПЕРАТИВ "ЖИЛИЩНЫЕ ВОЗМОЖНОСТИ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '16',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17739688,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0507-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6608001425',
      COLUMN_1: '17',
      COLUMN_4: 'ООО КБ "КОЛЬЦО УРАЛА", в г.Учалы',
      COLUMN_3: '65/5',
      COLUMN_0: '',
      key: '17',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 18491474,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05c8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7826667390',
      COLUMN_1: '18',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ СТРАХОВАЯ КОМПАНИЯ "КАПИТАЛ-ПОЛИС МЕДИЦИНА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '18',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21596051,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05ac-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6829000028',
      COLUMN_1: '19',
      COLUMN_4: 'АО Банк "ТКПБ"',
      COLUMN_3: '1312',
      COLUMN_0: '',
      key: '19',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 17439725,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-052b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5406119655',
      COLUMN_1: '20',
      COLUMN_4: 'Закрытое акционерное общество "ЗОЛОТАЯ КОРОНА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '20',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 24680803,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-054f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7715228310',
      COLUMN_1: '21',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "АЛЬФАСТРАХОВАНИЕ-ЖИЗНЬ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '21',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740116,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-051c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702021163',
      COLUMN_1: '22',
      COLUMN_4: 'ПАО АКБ "АВАНГАРД"',
      COLUMN_3: '2879',
      COLUMN_0: '',
      key: '22',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22865708,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0563-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4026006420',
      COLUMN_1: '23',
      COLUMN_4: 'АО "Газэнергобанк"',
      COLUMN_3: '3252',
      COLUMN_0: '',
      key: '23',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439203,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-053d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706625952',
      COLUMN_1: '24',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СОСЬЕТЕ ЖЕНЕРАЛЬ СТРАХОВАНИЕ ЖИЗНИ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '24',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 2968765,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04c4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703367904',
      COLUMN_1: '25',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "АОН РУС - СТРАХОВЫЕ БРОКЕРЫ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '25',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439546,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0519-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3457004183',
      COLUMN_1: '26',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ЛОМБАРД "ГРАНАТОВЫЙ БРАСЛЕТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '26',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937181,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0489-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7604289525',
      COLUMN_1: '27',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "МЕЖДУНАРОДНАЯ ПЛАТЕЖНАЯ СИСТЕМА "123" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '27',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26130181,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-057f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7704001959',
      COLUMN_1: '28',
      COLUMN_4: 'АО РОСЭКСИМБАНК',
      COLUMN_3: '2790',
      COLUMN_0: '',
      key: '28',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 21637760,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-05ba-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3232005484',
      COLUMN_1: '29',
      COLUMN_4: 'ПАО "Почта Банк"',
      COLUMN_3: '650',
      COLUMN_0: '',
      key: '29',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 25800402,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0552-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0274062111',
      COLUMN_1: '30',
      COLUMN_4: 'ПАО "БАНК УРАЛСИБ"',
      COLUMN_3: '2275',
      COLUMN_0: '',
      key: '30',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439566,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0515-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3436114146',
      COLUMN_1: '31',
      COLUMN_4:
        'НЕКОММЕРЧЕСКАЯ КОРПОРАТИВНАЯ ОРГАНИЗАЦИЯ "НАЦИОНАЛЬНОЕ ПОТРЕБИТЕЛЬСКОЕ ОБЩЕСТВО ВЗАИМНОГО СТРАХОВАНИЯ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '31',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24643385,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0543-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1653011835',
      COLUMN_1: '32',
      COLUMN_4: 'АКБ "Энергобанк" (АО)',
      COLUMN_3: '67',
      COLUMN_0: '',
      key: '32',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3004142,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04a8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7715781186',
      COLUMN_1: '33',
      COLUMN_4: 'Общество с ограниченной ответственностью "ЭнергоХолдинг" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '33',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2979490,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04c6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5612031491',
      COLUMN_1: '34',
      COLUMN_4: 'АО "БАНК ОРЕНБУРГ"',
      COLUMN_3: '3269',
      COLUMN_0: '',
      key: '34',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25166382,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05c6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7725114488',
      COLUMN_1: '35',
      COLUMN_4: 'АО "Россельхозбанк", Читинский региональный',
      COLUMN_3: '3349/47',
      COLUMN_0: '',
      key: '35',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3041646,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04e7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7707083893',
      COLUMN_1: '36',
      COLUMN_4: 'ПАО Сбербанк',
      COLUMN_3: '1481',
      COLUMN_0: '',
      key: '36',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937154,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-049b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701918296',
      COLUMN_1: '37',
      COLUMN_4:
        'Акционерное общество Микрофинансовая компания "Городская сберегательная касса" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '37',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637523,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6453033870',
      COLUMN_1: '38',
      COLUMN_4: '(АО "Банк "Агророс")',
      COLUMN_3: '2860',
      COLUMN_0: '',
      key: '38',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17779264,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05a5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710050376',
      COLUMN_1: '39',
      COLUMN_4: 'АО АКБ "Алеф-Банк"',
      COLUMN_3: '2119',
      COLUMN_0: '',
      key: '39',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720091,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-055c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710142732',
      COLUMN_1: '40',
      COLUMN_4: 'КБ "Крокус-Банк" (ООО)',
      COLUMN_3: '2682',
      COLUMN_0: '',
      key: '40',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26120115,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0566-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706150949',
      COLUMN_1: '41',
      COLUMN_4: 'Акционерное общество "Солид Менеджмент" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '41',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2949790,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04c0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7303024532',
      COLUMN_1: '42',
      COLUMN_4: 'АО Банк "Венец"',
      COLUMN_3: '524',
      COLUMN_0: '',
      key: '42',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439488,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-050a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3526027667',
      COLUMN_1: '43',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "СОДЕЙСТВИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '43',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439194,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0534-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706548313',
      COLUMN_1: '44',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "КАПИТАЛ ЛАЙФ СТРАХОВАНИЕ ЖИЗНИ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '44',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439495,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-050b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5321087612',
      COLUMN_1: '45',
      COLUMN_4:
        'Союз Саморегулируемая организация "Губернское кредитное содружество" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '45',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 24403140,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0541-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5407496776',
      COLUMN_1: '46',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрофинансовая компания "Джой Мани" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '46',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3077551,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04ec-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7831001239',
      COLUMN_1: '47',
      COLUMN_4: 'АО "ГОРБАНК"',
      COLUMN_3: '2982',
      COLUMN_0: '',
      key: '47',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937208,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-049a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7411071508',
      COLUMN_1: '48',
      COLUMN_4:
        'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ ГРАЖДАН ЮЖНОГО УРАЛА "СОДЕЙСТВИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '48',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19714830,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-055a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2204000595',
      COLUMN_1: '49',
      COLUMN_4: 'АО КБ "Модульбанк"',
      COLUMN_3: '1927',
      COLUMN_0: '',
      key: '49',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17439432,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0504-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9709035592',
      COLUMN_1: '50',
      COLUMN_4:
        'Акционерное общество Негосударственный пенсионный фонд "Атомгарант" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '50',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937201,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0494-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5262313112',
      COLUMN_1: '51',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ЭКОНОМЪ-КРЕДИТ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '51',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 22867381,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0579-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7712034098',
      COLUMN_1: '52',
      COLUMN_4: 'ООО КБ "Евроазиатский Инвестиционный Банк"',
      COLUMN_3: '2897',
      COLUMN_0: '',
      key: '52',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439682,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0536-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7456017116',
      COLUMN_1: '53',
      COLUMN_4: 'Закрытое акционерное общество "СвичМастер.РУ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '53',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3041640,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04e5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7705205000',
      COLUMN_1: '54',
      COLUMN_4: 'АО "Денизбанк Москва"',
      COLUMN_3: '3330',
      COLUMN_0: '',
      key: '54',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 27027888,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-05a2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702077840',
      COLUMN_1: '55',
      COLUMN_4: 'Публичное акционерное общество "Московская Биржа ММВБ-РТС"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '55',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17773795,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05a4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4825005381',
      COLUMN_1: '56',
      COLUMN_4: 'ПАО "Липецккомбанк"',
      COLUMN_3: '1242',
      COLUMN_0: '',
      key: '56',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22976218,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0586-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2465029704',
      COLUMN_1: '57',
      COLUMN_4: 'КБ "МКБ" (ПАО)',
      COLUMN_3: '2524',
      COLUMN_0: '',
      key: '57',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439511,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-050c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7840060671',
      COLUMN_1: '58',
      COLUMN_4: 'КИТ Финанс (Акционерное общество)',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '58',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 3037608,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04db-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '8603001714',
      COLUMN_1: '59',
      COLUMN_4: 'АО БАНК "Ермак"',
      COLUMN_3: '1809',
      COLUMN_0: '',
      key: '59',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 13602774,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0501-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4101019774',
      COLUMN_1: '60',
      COLUMN_4: 'ПАО "Камчаткомагропромбанк"',
      COLUMN_3: '545',
      COLUMN_0: '',
      key: '60',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 17439550,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0514-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9201007884',
      COLUMN_1: '61',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Управляющая компания "Парангон" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '61',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937190,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-048a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7825331045',
      COLUMN_1: '62',
      COLUMN_4: 'Акционерное общество "Санкт-Петербургская Валютная Биржа" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '62',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2978028,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04b2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5407257111',
      COLUMN_1: '63',
      COLUMN_4:
        'НОВОСИБИРСКИЙ ФИЛИАЛ ОБЩЕСТВА С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "МЕЖРЕГИОНАЛЬНЫЙ СПЕЦИАЛИЗИРОВАННЫЙ ДЕПОЗИТАРИЙ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '63',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 27266230,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-05b0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005605',
      COLUMN_1: '64',
      COLUMN_4: 'ООО "Фольксваген Банк РУС"',
      COLUMN_3: '3500',
      COLUMN_0: '',
      key: '64',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 18343854,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05c3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4026005138',
      COLUMN_1: '65',
      COLUMN_4: 'ООО банк "Элита"',
      COLUMN_3: '1399',
      COLUMN_0: '',
      key: '65',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937211,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-049c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4212125577',
      COLUMN_1: '66',
      COLUMN_4:
        'Кредитный потребительский кооператив "Кредитный союз "Солидарность" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '66',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3077553,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04ed-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750004270',
      COLUMN_1: '67',
      COLUMN_4: 'АО "Банк Финсервис"',
      COLUMN_3: '3388',
      COLUMN_0: '',
      key: '67',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 17439686,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0537-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2722103753',
      COLUMN_1: '68',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ МИКРОКРЕДИТНАЯ КОМПАНИЯ "ВЕБ-ЗАЙМ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '68',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24644770,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0544-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6654001613',
      COLUMN_1: '69',
      COLUMN_4: 'ООО КБ "Уралфинанс"',
      COLUMN_3: '1370',
      COLUMN_0: '',
      key: '69',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439258,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0530-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7730058711',
      COLUMN_1: '70',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "СТРАХОВАЯ КОМПАНИЯ МЕТЛАЙФ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '70',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937168,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04a5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7707179242',
      COLUMN_1: '71',
      COLUMN_4: 'Акционерное общество "Регистраторское общество "СТАТУС" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '71',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19696014,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-054b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6509006951',
      COLUMN_1: '72',
      COLUMN_4: 'АО РНКО "ХОЛМСК"',
      COLUMN_3: '503',
      COLUMN_0: '',
      key: '72',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3001328,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04b4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701105460',
      COLUMN_1: '73',
      COLUMN_4: 'РНКБ Банк (ПАО)',
      COLUMN_3: '1354',
      COLUMN_0: '',
      key: '73',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439538,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0518-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5035036060',
      COLUMN_1: '74',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "РАЗВИТИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '74',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439307,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0538-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701720592',
      COLUMN_1: '75',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Столичное Кредитное Бюро" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '75',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937193,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-048d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3436107565',
      COLUMN_1: '76',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "АЛЬТЕРНАТИВА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '76',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26120110,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0565-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701008530',
      COLUMN_1: '77',
      COLUMN_4: 'Акционерное общество "ИНВЕСТИЦИОННАЯ КОМПАНИЯ "ЕВРОФИНАНСЫ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '77',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26674716,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0598-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7728329636',
      COLUMN_1: '78',
      COLUMN_4:
        'Акционерное общество "Негосударственный пенсионный фонд "Внешэкономфонд" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '78',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637588,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4218004258',
      COLUMN_1: '79',
      COLUMN_4: '"БСТ-БАНК" АО',
      COLUMN_3: '2883',
      COLUMN_0: '',
      key: '79',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3038741,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04dc-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702015515',
      COLUMN_1: '80',
      COLUMN_4: 'Общество с ограниченной ответственностью "АТОН" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '80',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 24972281,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-05bf-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701548736',
      COLUMN_1: '81',
      COLUMN_4: 'Акционерное общество "УПРАВЛЯЮЩАЯ КОМПАНИЯ "ЕВРОФИНАНСЫ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '81',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26635159,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0594-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4101011782',
      COLUMN_1: '82',
      COLUMN_4: 'АО "Солид Банк"',
      COLUMN_3: '1329',
      COLUMN_0: '',
      key: '82',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720669,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0574-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1901036580',
      COLUMN_1: '83',
      COLUMN_4: 'ООО "Хакасский муниципальный банк"',
      COLUMN_3: '1049',
      COLUMN_0: '',
      key: '83',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2978858,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04c5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4401008879',
      COLUMN_1: '84',
      COLUMN_4: 'ООО КБ "Аксонбанк"',
      COLUMN_3: '680',
      COLUMN_0: '',
      key: '84',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2988306,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04d0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005531',
      COLUMN_1: '85',
      COLUMN_4: 'Коммерческий банк "ВРБ" (ООО)',
      COLUMN_3: '3499',
      COLUMN_0: '',
      key: '85',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25168406,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05c7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7708018456',
      COLUMN_1: '86',
      COLUMN_4: 'АО "НДБанк"',
      COLUMN_3: '2374',
      COLUMN_0: '',
      key: '86',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 27245487,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05a3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744002187',
      COLUMN_1: '87',
      COLUMN_4: 'ПАО КБ "ПФС-БАНК"',
      COLUMN_3: '2410',
      COLUMN_0: '',
      key: '87',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 13606928,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04f6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6829000290',
      COLUMN_1: '88',
      COLUMN_4: 'Банк "СЕРВИС РЕЗЕРВ" (АО)',
      COLUMN_3: '2034',
      COLUMN_0: '',
      key: '88',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3031035,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04d7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3006000387',
      COLUMN_1: '89',
      COLUMN_4: 'АО Банк "ККБ"',
      COLUMN_3: '1087',
      COLUMN_0: '',
      key: '89',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937148,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-048f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706548313',
      COLUMN_1: '90',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СТРАХОВАЯ КОМПАНИЯ "РОСГОССТРАХ-ЖИЗНЬ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '90',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937147,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-048e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7704216908',
      COLUMN_1: '91',
      COLUMN_4: 'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ПРОМИНСТРАХ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '91',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26122567,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0568-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6439044245',
      COLUMN_1: '92',
      COLUMN_4: 'АО "БАЛАКОВО-БАНК"',
      COLUMN_3: '444',
      COLUMN_0: '',
      key: '92',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 2937167,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04a4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7705519077',
      COLUMN_1: '93',
      COLUMN_4:
        'Акционерное общество "Негосударственный пенсионный фонд "Гефест"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '93',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 4130088,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04fa-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744002405',
      COLUMN_1: '94',
      COLUMN_4: '"БНП ПАРИБА БАНК" АО',
      COLUMN_3: '3407',
      COLUMN_0: '',
      key: '94',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439519,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-050e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6134012094',
      COLUMN_1: '95',
      COLUMN_4:
        'Общество с ограниченной ответственностью микрокредитная компания "Микрофинансовые услуги Дон" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '95',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 23852201,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-05c2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7727753970',
      COLUMN_1: '96',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрофинансовая компания "Кредит 911" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '96',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439444,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0505-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703795138',
      COLUMN_1: '97',
      COLUMN_4: 'Общество с ограниченной ответственностью "ЮнионПэй" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '97',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439572,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-051a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6312013969',
      COLUMN_1: '98',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "ОБЪЕДИНЕННАЯ СТРАХОВАЯ КОМПАНИЯ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '98',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21154997,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-059a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7831000108',
      COLUMN_1: '99',
      COLUMN_4: 'Таврический Банк (АО), Московский',
      COLUMN_3: '2304/5',
      COLUMN_0: '',
      key: '99',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21589947,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05a8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2503001251',
      COLUMN_1: '100',
      COLUMN_4: 'ПАО КБ "САММИТ БАНК"',
      COLUMN_3: '85',
      COLUMN_0: '',
      key: '100',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937153,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04ba-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7734249643',
      COLUMN_1: '101',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "БРИТАНСКИЙ СТРАХОВОЙ ДОМ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '101',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937169,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04a6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7707592234',
      COLUMN_1: '102',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "НАЦИОНАЛЬНАЯ КАСТОДИАЛЬНАЯ КОМПАНИЯ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '102',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26126784,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-057a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706027060',
      COLUMN_1: '103',
      COLUMN_4: 'АКБ "БЭНК ОФ ЧАЙНА" (АО)',
      COLUMN_3: '2309',
      COLUMN_0: '',
      key: '103',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 24674856,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0547-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7713056834',
      COLUMN_1: '104',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "АЛЬФАСТРАХОВАНИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '104',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439181,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0533-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703032986',
      COLUMN_1: '105',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "СТРАХОВАЯ ГРУППА "УРАЛСИБ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '105',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19894636,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0589-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005820',
      COLUMN_1: '106',
      COLUMN_4: 'АО "ГЕНБАНК"',
      COLUMN_3: '2490',
      COLUMN_0: '',
      key: '106',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 21707773,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05c0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750004094',
      COLUMN_1: '107',
      COLUMN_4: 'Банк "Кузнецкий мост" АО',
      COLUMN_3: '2254',
      COLUMN_0: '',
      key: '107',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 2937172,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0484-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7716051506',
      COLUMN_1: '108',
      COLUMN_4:
        'Акционерное общество Инвестиционная Компания "ЦЕРИХ Кэпитал Менеджмент"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '108',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 22699491,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-054d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5254004350',
      COLUMN_1: '109',
      COLUMN_4: 'ПАО "САРОВБИЗНЕСБАНК"',
      COLUMN_3: '2048',
      COLUMN_0: '',
      key: '109',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3488270,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4221026300',
      COLUMN_1: '110',
      COLUMN_4: 'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ЛОМБАРД -НК" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '110',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3077550,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04eb-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7726000596',
      COLUMN_1: '111',
      COLUMN_4: 'АКБ "СЛАВИЯ" (АО)',
      COLUMN_3: '2664',
      COLUMN_0: '',
      key: '111',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26628552,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-058d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7735104496',
      COLUMN_1: '112',
      COLUMN_4:
        'Микрокредитная компания "Купи не копи" (Общество с ограниченной ответственностью)',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '112',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19894612,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0588-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7831000122',
      COLUMN_1: '113',
      COLUMN_4: 'АО "АБ "РОССИЯ"',
      COLUMN_3: '328',
      COLUMN_0: '',
      key: '113',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439699,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0540-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710490352',
      COLUMN_1: '114',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрофинансовая компания "МангоФинанс" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '114',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25718773,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0554-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5000001042',
      COLUMN_1: '115',
      COLUMN_4: 'Банк "Возрождение" (ПАО)',
      COLUMN_3: '1439',
      COLUMN_0: '',
      key: '115',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3004144,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04aa-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7720177163',
      COLUMN_1: '116',
      COLUMN_4: 'Акционерное общество "Финанс сити Лтд" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '116',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 22404217,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-05d5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702165310',
      COLUMN_1: '117',
      COLUMN_4:
        'Небанковская кредитная организация акционерное общество "Национальный расчетный депозитарий" ',
      COLUMN_3: '3294',
      COLUMN_0: '',
      key: '117',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937204,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0495-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6321391646',
      COLUMN_1: '118',
      COLUMN_4:
        'АКЦИОНЕРНОЕ ОБЩЕСТВО "НЕГОСУДАРСТВЕННЫЙ ПЕНСИОННЫЙ ФОНД АВТОВАЗ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '118',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25483849,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05d4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7705233021',
      COLUMN_1: '119',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ СТРАХОВАЯ КОМПАНИЯ "ПАРИТЕТ - СК" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '119',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3041639,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04e3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7731044736',
      COLUMN_1: '120',
      COLUMN_4: 'АКБ "НБВК" (АО)',
      COLUMN_3: '3214',
      COLUMN_0: '',
      key: '120',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2999756,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04be-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701020946',
      COLUMN_1: '121',
      COLUMN_4: 'АО "Нефтепромбанк"',
      COLUMN_3: '2156',
      COLUMN_0: '',
      key: '121',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3077678,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04ef-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5503067018',
      COLUMN_1: '122',
      COLUMN_4: 'АО "РН Банк"',
      COLUMN_3: '170',
      COLUMN_0: '',
      key: '122',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937180,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0486-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9715010155',
      COLUMN_1: '123',
      COLUMN_4: 'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "АКТИВ ИНВЕСТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '123',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 20328905,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0590-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7725114488',
      COLUMN_1: '124',
      COLUMN_4: 'АО "Россельхозбанк", Костромской региональный',
      COLUMN_3: '3349/51',
      COLUMN_0: '',
      key: '124',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 13592264,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0500-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1653016689',
      COLUMN_1: '125',
      COLUMN_4: '"Тимер Банк" (ПАО)',
      COLUMN_3: '1581',
      COLUMN_0: '',
      key: '125',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22865699,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0562-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5260059340',
      COLUMN_1: '126',
      COLUMN_4: 'АО "КОШЕЛЕВ-БАНК"',
      COLUMN_3: '3300',
      COLUMN_0: '',
      key: '126',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937155,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0492-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703739359',
      COLUMN_1: '127',
      COLUMN_4:
        'Общество с ограниченной ответственностью микрокредитная компания Займ-Экспресс ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '127',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21596001,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05aa-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7714060199',
      COLUMN_1: '128',
      COLUMN_4: 'МОРСКОЙ БАНК (АО)',
      COLUMN_3: '77',
      COLUMN_0: '',
      key: '128',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 04 2023год',
      rowId: {
        date: '31.03.2023 00:00',
        auditId: 302994007,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 04 2023год',
        comment: '',
        id: '348bb73f-0583-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '04.2023',
      COLUMN_5: '4',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3906900937',
      COLUMN_1: '129',
      COLUMN_4: 'Неолант тенакс',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '129',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740781,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0524-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6166003409',
      COLUMN_1: '130',
      COLUMN_4: 'ПАО КБ "Сельмашбанк"',
      COLUMN_3: '106',
      COLUMN_0: '',
      key: '130',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 23300645,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-059b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4401183976',
      COLUMN_1: '131',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Микрокредитная компания "Соцветие" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '131',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 27270713,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05b1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7733812126',
      COLUMN_1: '132',
      COLUMN_4:
        'Общество с ограниченной ответственностью микрофинансовая компания "ВЭББАНКИР" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '132',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 23715038,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05bb-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6163011391',
      COLUMN_1: '133',
      COLUMN_4: 'ПАО КБ "Центр-инвест"',
      COLUMN_3: '2225',
      COLUMN_0: '',
      key: '133',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740130,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-051f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7713001271',
      COLUMN_1: '134',
      COLUMN_4: 'Джей энд Ти Банк (АО)',
      COLUMN_3: '3061',
      COLUMN_0: '',
      key: '134',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26130191,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0580-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744000398',
      COLUMN_1: '135',
      COLUMN_4: 'АО "Нордеа Банк"',
      COLUMN_3: '3016',
      COLUMN_0: '',
      key: '135',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937178,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0485-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9705044356',
      COLUMN_1: '136',
      COLUMN_4:
        'Акционерное общество "Негосударственный Пенсионный Фонд "Транснефть" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '136',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3039582,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04de-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7725497022',
      COLUMN_1: '137',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "ГРУППА РЕНЕССАНС СТРАХОВАНИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '137',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2953271,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04c7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4345375135',
      COLUMN_1: '138',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "КРЕДИТНЫЙ КЛУБ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '138',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2970134,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04c9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3906105745',
      COLUMN_1: '139',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "СВОЙ ДОМ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '139',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937206,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0498-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6685079610',
      COLUMN_1: '140',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрокредитная компания "Содействие XXI" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '140',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26605455,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-058c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744000912',
      COLUMN_1: '141',
      COLUMN_4: 'ПАО "Промсвязьбанк"',
      COLUMN_3: '3251',
      COLUMN_0: '',
      key: '141',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26126474,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-056a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7725114488',
      COLUMN_1: '142',
      COLUMN_4: 'АО "Россельхозбанк", Тувинский региональный',
      COLUMN_3: '3349/57',
      COLUMN_0: '',
      key: '142',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 23807688,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-05bd-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7715825027',
      COLUMN_1: '143',
      COLUMN_4:
        'Общество с ограниченной ответственностью микрофинансовая компания "МигКредит" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '143',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17439526,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-050f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6168073384',
      COLUMN_1: '144',
      COLUMN_4:
        'Общество с ограниченной ответственностью МИКРОКРЕДИТНАЯ КОМПАНИЯ "ЦМК АВАНС" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '144',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 3041645,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04e6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702070139',
      COLUMN_1: '145',
      COLUMN_4: 'Банк ВТБ (ПАО)',
      COLUMN_3: '1000',
      COLUMN_0: '',
      key: '145',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3306407,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4221027400',
      COLUMN_1: '146',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "КУЗНЕЦКИЕ ЛОМБАРДЫ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '146',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25797961,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0551-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2332006024',
      COLUMN_1: '147',
      COLUMN_4: 'ООО КБ "РостФинанс"',
      COLUMN_3: '481',
      COLUMN_0: '',
      key: '147',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937184,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0488-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710759236',
      COLUMN_1: '148',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Платежная система "Виза" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '148',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937221,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-049f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2536302906',
      COLUMN_1: '149',
      COLUMN_4:
        'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ИНВЕСТИЦИОННЫЙ ФОНД РАЗВИТИЯ БИЗНЕСА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '149',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937194,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0490-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3444107142',
      COLUMN_1: '150',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ДИАМАНТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '150',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3004143,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04a9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5406755146',
      COLUMN_1: '151',
      COLUMN_4: 'Общество с ограниченной ответственностью "Абилити Капитал" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '151',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637521,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5190900165',
      COLUMN_1: '152',
      COLUMN_4: 'БАНК "МСКБ" (АО)',
      COLUMN_3: '2722',
      COLUMN_0: '',
      key: '152',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637561,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744002652',
      COLUMN_1: '153',
      COLUMN_4: 'КБ "Новый век" (ООО)',
      COLUMN_3: '3417',
      COLUMN_0: '',
      key: '153',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24124241,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05d2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750003982',
      COLUMN_1: '154',
      COLUMN_4: 'ООО "Ю Би Эс Банк"',
      COLUMN_3: '3463',
      COLUMN_0: '',
      key: '154',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2972357,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04cd-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5260355389',
      COLUMN_1: '155',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "МИКРОФИНАНСОВАЯ КОМПАНИЯ  ЗАЙМИГО"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '155',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740143,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-051d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7709056550',
      COLUMN_1: '156',
      COLUMN_4: 'АО "Банк ЖилФинанс"',
      COLUMN_3: '3138',
      COLUMN_0: '',
      key: '156',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2983491,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04ca-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7704019762',
      COLUMN_1: '157',
      COLUMN_4: 'ООО РНКО "РИБ"',
      COLUMN_3: '2749',
      COLUMN_0: '',
      key: '157',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2023год',
      rowId: {
        date: '31.07.2023 00:00',
        auditId: 302994011,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2023год',
        comment: '',
        id: '348bb73f-0584-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2023',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3906900937',
      COLUMN_1: '158',
      COLUMN_4: 'Неолант тенакс',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '158',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 25731393,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0550-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5008009854',
      COLUMN_1: '159',
      COLUMN_4:
        'Акционерное общество Инвестиционно - финансовая компания "Солид" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '159',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439624,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0529-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1655225960',
      COLUMN_1: '160',
      COLUMN_4:
        'Саморегулируемая организация Союз микрофинансовых организаций "Единство"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '160',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637452,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05ae-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703211512',
      COLUMN_1: '161',
      COLUMN_4: 'АКБ "НРБанк" (АО)',
      COLUMN_3: '2170',
      COLUMN_0: '',
      key: '161',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3077676,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04ee-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7736153344',
      COLUMN_1: '162',
      COLUMN_4: 'Банк "ВБРР" (АО)',
      COLUMN_3: '3287',
      COLUMN_0: '',
      key: '162',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 2978506,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04c2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6161074565',
      COLUMN_1: '163',
      COLUMN_4:
        'АКЦИОНЕРНОЕ ОБЩЕСТВО НЕГОСУДАРСТВЕННЫЙ ПЕНСИОННЫЙ ФОНД "РОСТВЕРТОЛ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '163',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 19271476,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0549-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0411006129',
      COLUMN_1: '164',
      COLUMN_4: 'АКБ "НООСФЕРА" (АО)',
      COLUMN_3: '2650',
      COLUMN_0: '',
      key: '164',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 24820790,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05be-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3128000088',
      COLUMN_1: '165',
      COLUMN_4: 'ООО "Осколбанк"',
      COLUMN_3: '1050',
      COLUMN_0: '',
      key: '165',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 23206719,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0597-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005700',
      COLUMN_1: '166',
      COLUMN_4: 'НКО "ЭПС" (ООО)',
      COLUMN_3: '3509',
      COLUMN_0: '',
      key: '166',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 13435249,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04ff-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9705044518',
      COLUMN_1: '167',
      COLUMN_4:
        'Акционерное общество Негосударственный пенсионный фонд "Атомфонд" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '167',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25169263,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05cd-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750004175',
      COLUMN_1: '168',
      COLUMN_4: 'АО "МТИ Банк"',
      COLUMN_3: '1052',
      COLUMN_0: '',
      key: '168',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637580,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005860',
      COLUMN_1: '169',
      COLUMN_4: 'ООО НКО "Расчетные Решения"',
      COLUMN_3: '3524',
      COLUMN_0: '',
      key: '169',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26122437,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0567-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1653018661',
      COLUMN_1: '170',
      COLUMN_4: 'ООО КБЭР "Банк Казани"',
      COLUMN_3: '708',
      COLUMN_0: '',
      key: '170',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 4130089,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04fb-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7736176542',
      COLUMN_1: '171',
      COLUMN_4: 'МБО "ОРГБАНК" (ООО)',
      COLUMN_3: '3312',
      COLUMN_0: '',
      key: '171',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937205,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0497-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6671049044',
      COLUMN_1: '172',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрокредитная компания "ХайТэк-Финанс" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '172',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439529,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0516-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1514010496',
      COLUMN_1: '173',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "КАПИТАЛЪ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '173',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 3004332,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04ac-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7709315684',
      COLUMN_1: '174',
      COLUMN_4: 'АО "Банк ФИНАМ"',
      COLUMN_3: '2799',
      COLUMN_0: '',
      key: '174',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720576,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0571-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702165310',
      COLUMN_1: '175',
      COLUMN_4: 'НКО АО НРД',
      COLUMN_3: '3294',
      COLUMN_0: '',
      key: '175',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17439733,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-052c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1435308226',
      COLUMN_1: '176',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрофинансовая компания "АЭБ Партнер" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '176',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2978357,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04c1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5460000016',
      COLUMN_1: '177',
      COLUMN_4: 'АО "БКС Банк"',
      COLUMN_3: '101',
      COLUMN_0: '',
      key: '177',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24646503,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0545-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4214005204',
      COLUMN_1: '178',
      COLUMN_4: 'АО "Углеметбанк"',
      COLUMN_3: '2997',
      COLUMN_0: '',
      key: '178',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720144,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-056f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7709345294',
      COLUMN_1: '179',
      COLUMN_4: 'АО Банк "Развитие-Столица"',
      COLUMN_3: '3013',
      COLUMN_0: '',
      key: '179',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19709121,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0559-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5902300019',
      COLUMN_1: '180',
      COLUMN_4: 'АО АКИБ "Почтобанк"',
      COLUMN_3: '1788',
      COLUMN_0: '',
      key: '180',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3041638,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04e2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7704001959',
      COLUMN_1: '181',
      COLUMN_4: 'АО РОСЭКСИМБАНК',
      COLUMN_3: '2790',
      COLUMN_0: '',
      key: '181',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 20652030,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0596-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6316028910',
      COLUMN_1: '182',
      COLUMN_4: 'АО КБ "Солидарность"',
      COLUMN_3: '554',
      COLUMN_0: '',
      key: '182',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439652,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-052d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5903111695',
      COLUMN_1: '183',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрокредитная компания "Актив Финанс Групп" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '183',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439412,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0503-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7726486023',
      COLUMN_1: '184',
      COLUMN_4:
        'Акционерное общество "Негосударственный пенсионный фонд ГАЗФОНД пенсионные накопления" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '184',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24106798,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05cb-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '8603010518',
      COLUMN_1: '185',
      COLUMN_4: 'АО КБ "Приобье"',
      COLUMN_3: '537',
      COLUMN_0: '',
      key: '185',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720619,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0572-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702216772',
      COLUMN_1: '186',
      COLUMN_4: 'ООО "Дойче Банк"',
      COLUMN_3: '3328',
      COLUMN_0: '',
      key: '186',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439616,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0521-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7325153683',
      COLUMN_1: '187',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ПЕНСИОННЫЕ СБЕРЕЖЕНИЯ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '187',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22867144,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0577-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7712014310',
      COLUMN_1: '188',
      COLUMN_4: 'ИНГ БАНК (ЕВРАЗИЯ) АО',
      COLUMN_3: '2495',
      COLUMN_0: '',
      key: '188',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439360,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-053b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7707424367',
      COLUMN_1: '189',
      COLUMN_4:
        'Акционерное общество "Негосударственный пенсионный фонд "БЛАГОСОСТОЯНИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '189',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2972675,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04ce-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5610032958',
      COLUMN_1: '190',
      COLUMN_4:
        'Оренбургский ипотечный коммерческий банк "Русь" (Общество с ограниченной ответственностью)',
      COLUMN_3: '704',
      COLUMN_0: '',
      key: '190',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 9864877,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04fc-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7721278534',
      COLUMN_1: '191',
      COLUMN_4:
        'Саморегулируемая организация Союз кредитных потребительских кооперативов "Народные кассы - Союзсберзайм" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '191',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24106845,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05cc-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706195570',
      COLUMN_1: '192',
      COLUMN_4: 'АО "ИШБАНК"',
      COLUMN_3: '2867',
      COLUMN_0: '',
      key: '192',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26127518,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-057b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005732',
      COLUMN_1: '193',
      COLUMN_4: 'ООО РНКО "Единая касса"',
      COLUMN_3: '3512',
      COLUMN_0: '',
      key: '193',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22743420,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-055e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005387',
      COLUMN_1: '194',
      COLUMN_4: 'ООО "Голдман Сакс Банк"',
      COLUMN_3: '3490',
      COLUMN_0: '',
      key: '194',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3376639,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4218104728',
      COLUMN_1: '195',
      COLUMN_4: 'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ЛОМБАРД -С" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '195',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740155,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-051e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7736153344',
      COLUMN_1: '196',
      COLUMN_4: 'Банк "ВБРР" (АО)',
      COLUMN_3: '3287',
      COLUMN_0: '',
      key: '196',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3001621,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04b5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4346001485',
      COLUMN_1: '197',
      COLUMN_4: 'ПАО "Норвик Банк"',
      COLUMN_3: '902',
      COLUMN_0: '',
      key: '197',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2985035,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04cf-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744002187',
      COLUMN_1: '198',
      COLUMN_4: 'ПАО КБ "ПФС-БАНК"',
      COLUMN_3: '2410',
      COLUMN_0: '',
      key: '198',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19706700,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0558-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7733043350',
      COLUMN_1: '199',
      COLUMN_4: '"СДМ-Банк" (ПАО)',
      COLUMN_3: '1637',
      COLUMN_0: '',
      key: '199',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 3090779,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04d4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7711063650',
      COLUMN_1: '200',
      COLUMN_4: 'АО КБ "Жилстройбанк"',
      COLUMN_3: '2769',
      COLUMN_0: '',
      key: '200',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22865851,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0575-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7000000130',
      COLUMN_1: '201',
      COLUMN_4: 'ПАО "Томскпромстройбанк"',
      COLUMN_3: '1720',
      COLUMN_0: '',
      key: '201',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3037607,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04da-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6132001298',
      COLUMN_1: '202',
      COLUMN_4: 'ООО "ЗЕМКОМБАНК"',
      COLUMN_3: '574',
      COLUMN_0: '',
      key: '202',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937220,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-049e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1435159327',
      COLUMN_1: '203',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "СТРАХОВАЯ КОМПАНИЯ "СТЕРХ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '203',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937163,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04a2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701100510',
      COLUMN_1: '204',
      COLUMN_4:
        'Акционерное общество "Национальный негосударственный пенсионный фонд"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '204',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2022год',
      rowId: {
        date: '29.12.2022 00:00',
        auditId: 7777,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2022год',
        comment: '',
        id: '348bb73f-0482-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2022',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7716032944',
      COLUMN_1: '205',
      COLUMN_4: 'Фактор-ТС',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '205',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22867128,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0576-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3525023780',
      COLUMN_1: '206',
      COLUMN_4: 'ПАО "БАНК СГБ"',
      COLUMN_3: '2816',
      COLUMN_0: '',
      key: '206',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 18491407,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05c4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7838066700',
      COLUMN_1: '207',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СТРАХОВАЯ КОМПАНИЯ "КАПИТАЛ-ПОЛИС" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '207',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439604,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-051b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1841074235',
      COLUMN_1: '208',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "СБЕРКНИЖКА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '208',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 13421524,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04f9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1326024785',
      COLUMN_1: '209',
      COLUMN_4: 'АКБ "АКТИВ БАНК" (ПАО)',
      COLUMN_3: '2529',
      COLUMN_0: '',
      key: '209',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17794450,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05a7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1650072068',
      COLUMN_1: '210',
      COLUMN_4: 'АО "Автоградбанк"',
      COLUMN_3: '1455',
      COLUMN_0: '',
      key: '210',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 3004756,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04ae-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2310050140',
      COLUMN_1: '211',
      COLUMN_4: 'Банк "Первомайский" (ПАО)',
      COLUMN_3: '518',
      COLUMN_0: '',
      key: '211',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937150,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0493-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7714829011',
      COLUMN_1: '212',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ СТРАХОВАЯ КОМПАНИЯ "АСКОР" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '212',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 13413990,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0709002625',
      COLUMN_1: '213',
      COLUMN_4: 'Банк "Прохладный" ООО',
      COLUMN_3: '874',
      COLUMN_0: '',
      key: '213',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 3041647,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04e8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7736046991',
      COLUMN_1: '214',
      COLUMN_4: 'АКБ "Абсолют Банк" (ПАО)',
      COLUMN_3: '2306',
      COLUMN_0: '',
      key: '214',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 23302379,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-059c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4402003136',
      COLUMN_1: '215',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "СБЕРЗАЙМ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '215',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2976553,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04d1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7831000965',
      COLUMN_1: '216',
      COLUMN_4: 'АО Банк "ПСКБ"',
      COLUMN_3: '2551',
      COLUMN_0: '',
      key: '216',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19705972,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0557-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6450013459',
      COLUMN_1: '217',
      COLUMN_4: 'АО "Экономбанк"',
      COLUMN_3: '1319',
      COLUMN_0: '',
      key: '217',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3060927,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04e9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1653003834',
      COLUMN_1: '218',
      COLUMN_4: 'АО "ТАТСОЦБАНК"',
      COLUMN_3: '480',
      COLUMN_0: '',
      key: '218',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2976638,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04d2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5243006236',
      COLUMN_1: '219',
      COLUMN_4: 'АО комбанк "Арзамас"',
      COLUMN_3: '1281',
      COLUMN_0: '',
      key: '219',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 17439497,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0508-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6732002913',
      COLUMN_1: '220',
      COLUMN_4:
        'Ассоциация "Саморегулируемая организация кредитных кооперативов "Содействие"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '220',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937042,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0487-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7831000066',
      COLUMN_1: '221',
      COLUMN_4: 'ПАО "Энергомашбанк"',
      COLUMN_3: '52',
      COLUMN_0: '',
      key: '221',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22756007,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0561-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3015011755',
      COLUMN_1: '222',
      COLUMN_4: 'АО ВКАБАНК',
      COLUMN_3: '1027',
      COLUMN_0: '',
      key: '222',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 27551062,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-056b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0901000990',
      COLUMN_1: '223',
      COLUMN_4: 'АО "Народный банк"',
      COLUMN_3: '2249',
      COLUMN_0: '',
      key: '223',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3039569,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04dd-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7730192918',
      COLUMN_1: '224',
      COLUMN_4:
        'Акционерное общество Негосударственный пенсионный фонд "Негосударственный Сберегательный Пенсионный Фонд" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '224',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937198,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0491-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1650244278',
      COLUMN_1: '225',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "МИКРОФИНАНСОВАЯ КОМПАНИЯ "ДЕНЬГИМИГОМ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '225',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 3060891,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04ea-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0901001063',
      COLUMN_1: '226',
      COLUMN_4: 'АО "Тексбанк"',
      COLUMN_3: '2756',
      COLUMN_0: '',
      key: '226',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26128855,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-057c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1435138944',
      COLUMN_1: '227',
      COLUMN_4: 'АКБ "Алмазэргиэнбанк" АО',
      COLUMN_3: '2602',
      COLUMN_0: '',
      key: '227',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439622,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0528-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1653001570',
      COLUMN_1: '228',
      COLUMN_4:
        'Акционерное Общество "Центральный Депозитарий Республики Татарстан" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '228',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439173,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0532-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7606001534',
      COLUMN_1: '229',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ СТРАХОВАЯ КОМПАНИЯ "УРАЛСИБ СТРАХОВАНИЕ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '229',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740783,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0525-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6164102186',
      COLUMN_1: '230',
      COLUMN_4: 'ПАО "Донкомбанк"',
      COLUMN_3: '492',
      COLUMN_0: '',
      key: '230',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439455,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0506-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9710019429',
      COLUMN_1: '231',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Оператор банковской кооперации"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '231',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2961333,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04bd-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750004263',
      COLUMN_1: '232',
      COLUMN_4: 'ООО "РАМ Банк"',
      COLUMN_3: '3480',
      COLUMN_0: '',
      key: '232',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439667,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0531-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7448094854',
      COLUMN_1: '233',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ ЛОМБАРД "ЗОЛОТАЯ РЫБКА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '233',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 27616836,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-056c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5047134390',
      COLUMN_1: '234',
      COLUMN_4:
        'общество с ограниченной ответственностью "Микрокредитная компания "Кредит Лайн" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '234',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 3000183,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04bf-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0703000942',
      COLUMN_1: '235',
      COLUMN_4: 'ООО "Банк "Майский"',
      COLUMN_3: '1673',
      COLUMN_0: '',
      key: '235',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2999755,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04b9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6227003906',
      COLUMN_1: '236',
      COLUMN_4: 'ООО "ЖИВАГО БАНК"',
      COLUMN_3: '2065',
      COLUMN_0: '',
      key: '236',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439665,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-052f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5906150822',
      COLUMN_1: '237',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ЛОМБАРД "СОХРАННАЯ КАЗНА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '237',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740741,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0523-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4216002921',
      COLUMN_1: '238',
      COLUMN_4: 'ООО "НОВОКИБ"',
      COLUMN_3: '1747',
      COLUMN_0: '',
      key: '238',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937191,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-048b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0104013283',
      COLUMN_1: '239',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "СВОЙ ДОМ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '239',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 17439314,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0539-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701109908',
      COLUMN_1: '240',
      COLUMN_4:
        'Негосударственный пенсионный фонд "Профессиональный" (Акционерное общество)',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '240',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22747722,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-055f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5047093433',
      COLUMN_1: '241',
      COLUMN_4: '"БМВ Банк" ООО',
      COLUMN_3: '3482',
      COLUMN_0: '',
      key: '241',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2978282,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04b3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5433107271',
      COLUMN_1: '242',
      COLUMN_4: 'КБ "Русский ипотечный банк" (ООО)',
      COLUMN_3: '1968',
      COLUMN_0: '',
      key: '242',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937048,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0496-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7734052372',
      COLUMN_1: '243',
      COLUMN_4: 'АКБ "ПРОМИНВЕСТБАНК" (ПАО)',
      COLUMN_3: '2433',
      COLUMN_0: '',
      key: '243',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 22279822,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05c9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7709138570',
      COLUMN_1: '244',
      COLUMN_4: 'ПАО АКБ "Металлинвестбанк"',
      COLUMN_3: '2440',
      COLUMN_0: '',
      key: '244',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2937170,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0483-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7714324003',
      COLUMN_1: '245',
      COLUMN_4:
        'Акционерное общество "Негосударственный Пенсионный Фонд "Социум"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '245',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3035133,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04d9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703074601',
      COLUMN_1: '246',
      COLUMN_4: 'АКБ "ПЕРЕСВЕТ" (АО)',
      COLUMN_3: '2110',
      COLUMN_0: '',
      key: '246',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637482,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7708050033',
      COLUMN_1: '247',
      COLUMN_4: '"ЗИРААТ БАНК (МОСКВА)" (АО)',
      COLUMN_3: '2559',
      COLUMN_0: '',
      key: '247',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17439372,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-053f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7709445387',
      COLUMN_1: '248',
      COLUMN_4:
        'Акционерное общество Негосударственный пенсионный фонд ВТБ Пенсионный фонд ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '248',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26423956,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0587-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7705041231',
      COLUMN_1: '249',
      COLUMN_4:
        'ПУБЛИЧНОЕ АКЦИОНЕРНОЕ ОБЩЕСТВО "СТРАХОВАЯ АКЦИОНЕРНАЯ КОМПАНИЯ "ЭНЕРГОГАРАНТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '249',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439356,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-053a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703379402',
      COLUMN_1: '250',
      COLUMN_4:
        'Акционерное общество Негосударственный пенсионный фонд "Альянс" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '250',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 20317254,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-058f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7728326674',
      COLUMN_1: '251',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "СИТИ СБЕРЪ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '251',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3322792,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04f2-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7712014310',
      COLUMN_1: '252',
      COLUMN_4: 'ИНГ БАНК (ЕВРАЗИЯ) АО',
      COLUMN_3: '2495',
      COLUMN_0: '',
      key: '252',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2957220,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04cb-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7724017756',
      COLUMN_1: '253',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СТРАХОВАЯ КОМПАНИЯ "РУССКАЯ КОРОНА" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '253',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 22173723,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-05c5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710541631',
      COLUMN_1: '254',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "АИГ СТРАХОВАЯ КОМПАНИЯ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '254',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26605358,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-058b-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7731513346',
      COLUMN_1: '255',
      COLUMN_4: 'Общество с ограниченной ответственностью "Оборонрегистр" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '255',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 24668960,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0546-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750004104',
      COLUMN_1: '256',
      COLUMN_4: 'Банк НФК (АО)',
      COLUMN_3: '3437',
      COLUMN_0: '',
      key: '256',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 23632977,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05af-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0541016015',
      COLUMN_1: '257',
      COLUMN_4: 'ООО КБ "ВНЕШФИНБАНК"',
      COLUMN_3: '3173',
      COLUMN_0: '',
      key: '257',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3041625,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04e1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '8603001619',
      COLUMN_1: '258',
      COLUMN_4: 'АО АБ "Капитал"',
      COLUMN_3: '575',
      COLUMN_0: '',
      key: '258',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 25805509,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0553-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005450',
      COLUMN_1: '259',
      COLUMN_4: 'АО "СМБСР Банк"',
      COLUMN_3: '3494',
      COLUMN_0: '',
      key: '259',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 3514626,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4220037807',
      COLUMN_1: '260',
      COLUMN_4: 'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ЛОМБАРД-ВЕСТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '260',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937207,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0499-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6612035593',
      COLUMN_1: '261',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "КАПИТАЛ-С" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '261',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24131203,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05d3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6608003052',
      COLUMN_1: '262',
      COLUMN_4: 'ПАО "СКБ-банк"',
      COLUMN_3: '705',
      COLUMN_0: '',
      key: '262',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17439516,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-050d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2310097324',
      COLUMN_1: '263',
      COLUMN_4:
        'Общество с ограниченной ответственностью микрокредитная компания "Краевой центр развития бизнеса" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '263',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439670,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0535-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6686058813',
      COLUMN_1: '264',
      COLUMN_4:
        'Акционерное общество Негосударственный пенсионный фонд "УГМК-Перспектива" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '264',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17739913,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0511-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7414006722',
      COLUMN_1: '265',
      COLUMN_4: 'Банк "КУБ" (АО)',
      COLUMN_3: '2584',
      COLUMN_0: '',
      key: '265',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720468,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0570-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750004168',
      COLUMN_1: '266',
      COLUMN_4: 'КБ "АКРОПОЛЬ" АО',
      COLUMN_3: '3027',
      COLUMN_0: '',
      key: '266',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 2937157,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04bb-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710561081',
      COLUMN_1: '267',
      COLUMN_4: 'Закрытое акционерное общество "Объединенное Кредитное Бюро" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '267',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21594242,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05a9-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6608001425',
      COLUMN_1: '268',
      COLUMN_4: 'ООО КБ "КОЛЬЦО УРАЛА"',
      COLUMN_3: '65',
      COLUMN_0: '',
      key: '268',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2976555,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04af-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744001070',
      COLUMN_1: '269',
      COLUMN_4: 'АО КБ "Соколовский"',
      COLUMN_3: '2830',
      COLUMN_0: '',
      key: '269',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 19271485,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-054a-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1700000350',
      COLUMN_1: '270',
      COLUMN_4: 'АО БАНК "НБТ"',
      COLUMN_3: '1309',
      COLUMN_0: '',
      key: '270',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17739564,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0527-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7449014065',
      COLUMN_1: '271',
      COLUMN_4: 'АО "УРАЛПРОМБАНК"',
      COLUMN_3: '2964',
      COLUMN_0: '',
      key: '271',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 27619431,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-056d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7726445147',
      COLUMN_1: '272',
      COLUMN_4:
        'Акционерное общество "Негосударственный пенсионный фонд ГАЗФОНД" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '272',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2937192,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-048c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1655217870',
      COLUMN_1: '273',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ФИНАНСЮГ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '273',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720632,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0573-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '9102019769',
      COLUMN_1: '274',
      COLUMN_4: 'АО "Банк ЧБРР"',
      COLUMN_3: '3527',
      COLUMN_0: '',
      key: '274',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 2968764,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-04c3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7714106679',
      COLUMN_1: '275',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО "МАРШ-СТРАХОВЫЕ БРОКЕРЫ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '275',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 22355706,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-05cf-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0546016675',
      COLUMN_1: '276',
      COLUMN_4: 'ООО КБ "Кредитинвест"',
      COLUMN_3: '1197',
      COLUMN_0: '',
      key: '276',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439660,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-052e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6625058238',
      COLUMN_1: '277',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ГОРОДСКОЙ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '277',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 22328438,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05ca-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6025001487',
      COLUMN_1: '278',
      COLUMN_4: 'АО КБ "ВАКОБАНК"',
      COLUMN_3: '1291',
      COLUMN_0: '',
      key: '278',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937011,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04a3-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702045051',
      COLUMN_1: '279',
      COLUMN_4: 'ПАО "МТС-Банк"',
      COLUMN_3: '2268',
      COLUMN_0: '',
      key: '279',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19720119,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-055d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7704111969',
      COLUMN_1: '280',
      COLUMN_4: 'ООО "Банк БКФ"',
      COLUMN_3: '2684',
      COLUMN_0: '',
      key: '280',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937158,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04bc-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7719886937',
      COLUMN_1: '281',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Специализированное Кредитное Бюро" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '281',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17785674,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05a6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3702951429',
      COLUMN_1: '282',
      COLUMN_4: 'ПНКО "ИНЭКО" (ООО)',
      COLUMN_3: '3520',
      COLUMN_0: '',
      key: '282',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 17439256,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0502-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7728534829',
      COLUMN_1: '283',
      COLUMN_4: 'АКЦИОНЕРНОЕ ОБЩЕСТВО СТРАХОВАЯ КОМПАНИЯ "УРАЛСИБ ЖИЗНЬ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '283',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637557,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744003007',
      COLUMN_1: '284',
      COLUMN_4: 'КБ "Рента-Банк" АО',
      COLUMN_3: '3095',
      COLUMN_0: '',
      key: '284',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17739942,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0512-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2703006553',
      COLUMN_1: '285',
      COLUMN_4: 'АО "Роял Кредит Банк"',
      COLUMN_3: '783',
      COLUMN_0: '',
      key: '285',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22865722,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0564-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7727051787',
      COLUMN_1: '286',
      COLUMN_4: 'АО «РОСКОСМОСБАНК»',
      COLUMN_3: '2989',
      COLUMN_0: '',
      key: '286',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 3091803,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04d5-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5026014060',
      COLUMN_1: '287',
      COLUMN_4: 'АО КБ "АГРОПРОМКРЕДИТ"',
      COLUMN_3: '2880',
      COLUMN_0: '',
      key: '287',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22752035,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0560-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6915001057',
      COLUMN_1: '288',
      COLUMN_4: 'ПАО "Банк "Торжок"',
      COLUMN_3: '933',
      COLUMN_0: '',
      key: '288',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 01 2023год',
      rowId: {
        date: '31.12.2022 00:00',
        auditId: 302994004,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 01 2023год',
        comment: '',
        id: '348bb73f-0582-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '01.2023',
      COLUMN_5: '1',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3906900937',
      COLUMN_1: '289',
      COLUMN_4: 'Неолант тенакс',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '289',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 22867146,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0578-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7727067410',
      COLUMN_1: '290',
      COLUMN_4: 'ООО "НКО "Вестерн Юнион ДП Восток"',
      COLUMN_3: '2726',
      COLUMN_0: '',
      key: '290',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3198180,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05d7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7736153344',
      COLUMN_1: '291',
      COLUMN_4: 'Акционерное общество "Всероссийский банк развития регионов"',
      COLUMN_3: '3287',
      COLUMN_0: '',
      key: '291',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21637570,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05b7-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7750005789',
      COLUMN_1: '292',
      COLUMN_4: 'ООО "Чайна Констракшн Банк"',
      COLUMN_3: '3515',
      COLUMN_0: '',
      key: '292',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26130163,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-057d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744000920',
      COLUMN_1: '293',
      COLUMN_4: 'КБ "Максима" (ООО)',
      COLUMN_3: '3379',
      COLUMN_0: '',
      key: '293',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2937222,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04a0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2721221313',
      COLUMN_1: '294',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "УМНОЖИТЬ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '294',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 24616229,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0542-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4202022325',
      COLUMN_1: '295',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ ГРАЖДАН "ВЗАИМНОСТЬ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '295',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24359956,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05d6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7730634468',
      COLUMN_1: '296',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрофинансовая компания "КарМани" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '296',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 25169285,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05ce-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7744000038',
      COLUMN_1: '297',
      COLUMN_4: 'АО АКБ "МЕЖДУНАРОДНЫЙ ФИНАНСОВЫЙ КЛУБ"',
      COLUMN_3: '2618',
      COLUMN_0: '',
      key: '297',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2937213,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-049d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7509003222',
      COLUMN_1: '298',
      COLUMN_4:
        'Сельскохозяйственный потребительский кредитный кооператив "Красночикойский"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '298',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26630192,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-058e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5904004343',
      COLUMN_1: '299',
      COLUMN_4: 'АКБ "Проинвестбанк" (ПАО)',
      COLUMN_3: '784',
      COLUMN_0: '',
      key: '299',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26631238,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0593-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7706096522',
      COLUMN_1: '300',
      COLUMN_4: 'ПАО АРКБ "Росбизнесбанк"',
      COLUMN_3: '1405',
      COLUMN_0: '',
      key: '300',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 22592880,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-054c-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7733783309',
      COLUMN_1: '301',
      COLUMN_4:
        'Общество с ограниченной ответственностью микрофинансовая компания "Центр Финансовой Поддержки" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '301',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 27649838,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0581-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6162070041',
      COLUMN_1: '302',
      COLUMN_4:
        'Общество с ограниченной ответственностью Микрофинансовая компания "НАДЕЖНЫЙ ИНВЕСТОР" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '302',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17439481,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-0509-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1001241307',
      COLUMN_1: '303',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ИЛМА-КРЕДИТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '303',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 3041264,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04e0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5406121446',
      COLUMN_1: '304',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Компания БрокерКредитСервис" (Московский филиал)',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '304',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 3030489,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-04d6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7705849950',
      COLUMN_1: '305',
      COLUMN_4:
        'Общество с ограниченной ответственностью "Страховая платежная система" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '305',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 24122576,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-05d1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7609016017',
      COLUMN_1: '306',
      COLUMN_4: 'КБ "РБА" (ООО)',
      COLUMN_3: '3413',
      COLUMN_0: '',
      key: '306',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2976631,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04b0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4216004076',
      COLUMN_1: '307',
      COLUMN_4: 'АО "Кузнецкбизнесбанк"',
      COLUMN_3: '1158',
      COLUMN_0: '',
      key: '307',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 23814804,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-05c1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7730060164',
      COLUMN_1: '308',
      COLUMN_4: 'ПАО РОСБАНК',
      COLUMN_3: '2272',
      COLUMN_0: '',
      key: '308',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 3004755,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04ad-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '0548002149',
      COLUMN_1: '309',
      COLUMN_4: 'ООО КБ "МВС Банк"',
      COLUMN_3: '2407',
      COLUMN_0: '',
      key: '309',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 2999753,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04b8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7423004062',
      COLUMN_1: '310',
      COLUMN_4: 'Банк "Снежинский" АО',
      COLUMN_3: '1376',
      COLUMN_0: '',
      key: '310',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 21596065,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05ad-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7701020946',
      COLUMN_1: '311',
      COLUMN_4: 'АО "Нефтепромбанк"',
      COLUMN_3: '2156',
      COLUMN_0: '',
      key: '311',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 27294076,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0555-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7703351333',
      COLUMN_1: '312',
      COLUMN_4: 'Акционерное Общество "Национальная товарная биржа" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '312',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 3041643,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-04e4-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '3232005484',
      COLUMN_1: '313',
      COLUMN_4: 'ПАО "Почта Банк"',
      COLUMN_3: '650',
      COLUMN_0: '',
      key: '313',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 17740535,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-05a0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '5902300072',
      COLUMN_1: '314',
      COLUMN_4: 'ПАО АКБ "Урал ФД"',
      COLUMN_3: '249',
      COLUMN_0: '',
      key: '314',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 25651175,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0548-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4218108151',
      COLUMN_1: '315',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ МИКРОКРЕДИТНАЯ КОМПАНИЯ "ГЛАВКРЕДИТ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '315',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 23807449,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05bc-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1650002455',
      COLUMN_1: '316',
      COLUMN_4: 'ПАО "АКИБАНК"',
      COLUMN_3: '2587',
      COLUMN_0: '',
      key: '316',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 22976214,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0585-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7707040963',
      COLUMN_1: '317',
      COLUMN_4: 'ООО КБ "Нэклис-Банк"',
      COLUMN_3: '1671',
      COLUMN_0: '',
      key: '317',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 13421523,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-04f8-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1300034972',
      COLUMN_1: '318',
      COLUMN_4: 'ПАО КБ "МПСБ"',
      COLUMN_3: '752',
      COLUMN_0: '',
      key: '318',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 22374835,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-05d0-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7714829011',
      COLUMN_1: '319',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ СТРАХОВАЯ КОМПАНИЯ "АСКОР" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '319',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Автоматический режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26915360,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-059e-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: true,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1658087965',
      COLUMN_1: '320',
      COLUMN_4:
        'НЕКОММЕРЧЕСКАЯ КОРПОРАТИВНАЯ ОРГАНИЗАЦИЯ ПОТРЕБИТЕЛЬСКОЕ ОВС "САКЛАУ"',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '320',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26925319,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-059f-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710045520',
      COLUMN_1: '321',
      COLUMN_4: 'СТРАХОВОЕ ПУБЛИЧНОЕ АКЦИОНЕРНОЕ ОБЩЕСТВО "РЕСО-ГАРАНТИЯ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '321',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 17439600,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0520-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '1655234210',
      COLUMN_1: '322',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ ГРАЖДАН "ГОЗЗАЙМ" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '322',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26812355,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-059d-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7702848563',
      COLUMN_1: '323',
      COLUMN_4:
        'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "СТРАХОВАЯ КОМПАНИЯ "МЕГАРУСС-Д" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '323',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 07 2020год',
      rowId: {
        date: '30.06.2020 00:00',
        auditId: 2954894,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 07 2020год',
        comment: '',
        id: '348bb73f-04b6-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '07.2020',
      COLUMN_5: '7',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7708031739',
      COLUMN_1: '324',
      COLUMN_4: 'ООО ПИР Банк',
      COLUMN_3: '2655',
      COLUMN_0: '',
      key: '324',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 11 2020год',
      rowId: {
        date: '31.10.2020 00:00',
        auditId: 17739550,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 11 2020год',
        comment: '',
        id: '348bb73f-0526-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '11.2020',
      COLUMN_5: '11',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7710401987',
      COLUMN_1: '325',
      COLUMN_4: 'АО КБ "Ситибанк"',
      COLUMN_3: '2557',
      COLUMN_0: '',
      key: '325',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 17740729,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-0522-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '6608001425',
      COLUMN_1: '326',
      COLUMN_4: 'ООО КБ "КОЛЬЦО УРАЛА", в г.Кемерово',
      COLUMN_3: '65/6',
      COLUMN_0: '',
      key: '326',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 08 2020год',
      rowId: {
        date: '31.07.2020 00:00',
        auditId: 19702871,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 08 2020год',
        comment: '',
        id: '348bb73f-0556-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '08.2020',
      COLUMN_5: '8',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7722076611',
      COLUMN_1: '327',
      COLUMN_4: 'АО "Собинбанк"',
      COLUMN_3: '1317',
      COLUMN_0: '',
      key: '327',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 12 2020год',
      rowId: {
        date: '30.11.2020 00:00',
        auditId: 26804243,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 12 2020год',
        comment: '',
        id: '348bb73f-0599-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '12.2020',
      COLUMN_5: '12',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '2536234300',
      COLUMN_1: '328',
      COLUMN_4: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "ЛИДЕРС" ',
      COLUMN_3: '',
      COLUMN_0: '',
      key: '328',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 09 2020год',
      rowId: {
        date: '31.08.2020 00:00',
        auditId: 2976646,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 09 2020год',
        comment: '',
        id: '348bb73f-04b1-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '09.2020',
      COLUMN_5: '9',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '7714038860',
      COLUMN_1: '329',
      COLUMN_4: 'ПАО РАКБ "МОСКВА"',
      COLUMN_3: '1661',
      COLUMN_0: '',
      key: '329',
    },
    {
      COLUMN_11: '',
      COLUMN_10: '',
      COLUMN_12: 'Ручной режим',
      COLUMN_9: 'Проверка 10 2020год',
      rowId: {
        date: '30.09.2020 00:00',
        auditId: 26635224,
        reason: '',
        checkedRows: false,
        checked: false,
        textDescription: 'Проверка 10 2020год',
        comment: '',
        id: '348bb73f-0595-8a2b-e063-0606010a2273',
        user: '',
        isAutomatic: false,
      },
      COLUMN_6: '10.2020',
      COLUMN_5: '10',
      COLUMN_8: 'Не запланировано',
      COLUMN_7: 1,
      tabTitle: '',
      COLUMN_2: '4101011782',
      COLUMN_1: '330',
      COLUMN_4: 'АО "Солид Банк", Московский',
      COLUMN_3: '1329/4',
      COLUMN_0: '',
      key: '330',
    },
  ],
  sort: null,
  group: null,
};
