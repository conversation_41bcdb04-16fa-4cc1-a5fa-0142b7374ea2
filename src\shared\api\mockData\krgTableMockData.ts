export const krgTableMockData = {
  columns: [
    {
      title: '№',
      dataIndex: 'COLUMN_1',
      key: 'COLUMN_1',
      columnType: 'java.lang.Long',
      width: 64,
      align: 'left',
      columnUuid: '1',
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Статус-состояние КРГ',
      dataIndex: 'COLUMN_2',
      key: 'COLUMN_2',
      columnType: 'java.lang.String',
      width: 176,
      align: 'left',
      columnUuid: '2',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Текстовое описание КРГ',
      dataIndex: 'COLUMN_3',
      key: 'COLUMN_3',
      columnType: 'java.lang.String',
      width: 320,
      align: 'left',
      columnUuid: '3',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Код КРГ',
      dataIndex: 'COLUMN_4',
      key: 'COLUMN_4',
      columnType:
        'ru.lanit.fpsid.kzid_backend.result_sets.renderers.CustomizedString',
      width: 204,
      align: 'left',
      columnUuid: '4',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'МЦИ',
      dataIndex: 'COLUMN_5',
      key: 'COLUMN_5',
      columnType: 'java.lang.String',
      width: 256,
      align: 'left',
      columnUuid: '5',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Инспекционное подразделение',
      dataIndex: 'COLUMN_6',
      key: 'COLUMN_6',
      columnType: 'java.lang.String',
      width: 256,
      align: 'left',
      columnUuid: '6',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Дата начала проверки',
      dataIndex: 'COLUMN_7',
      key: 'COLUMN_7',
      columnType: 'java.sql.Timestamp',
      width: 256,
      align: 'left',
      columnUuid: '7',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Месяц начала проверки',
      dataIndex: 'COLUMN_8',
      key: 'COLUMN_8',
      columnType: 'java.lang.String',
      width: 112,
      align: 'left',
      columnUuid: '8',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Предполагаемая дата завершения проверки',
      dataIndex: 'COLUMN_9',
      key: 'COLUMN_9',
      columnType: 'java.sql.Timestamp',
      width: 256,
      align: 'left',
      columnUuid: '9',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Дата завершения проверки',
      dataIndex: 'COLUMN_10',
      key: 'COLUMN_10',
      columnType: 'java.sql.Timestamp',
      width: 256,
      align: 'left',
      columnUuid: '10',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Наименование ПЛ',
      dataIndex: 'COLUMN_11',
      key: 'COLUMN_11',
      columnType: 'java.lang.String',
      width: 768,
      align: 'left',
      columnUuid: '11',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Юридический адрес ПЛ',
      dataIndex: 'COLUMN_13',
      key: 'COLUMN_13',
      columnType: 'java.lang.String',
      width: 768,
      align: 'left',
      columnUuid: '13',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Фактический адрес ПЛ',
      dataIndex: 'COLUMN_14',
      key: 'COLUMN_14',
      columnType: 'java.lang.String',
      width: 768,
      align: 'left',
      columnUuid: '14',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'ИНН ПЛ',
      dataIndex: 'COLUMN_15',
      key: 'COLUMN_15',
      columnType: 'java.lang.String',
      width: 281,
      align: 'left',
      columnUuid: '15',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'ОГРН ПЛ',
      dataIndex: 'COLUMN_16',
      key: 'COLUMN_16',
      columnType: 'java.lang.String',
      width: 281,
      align: 'left',
      columnUuid: '16',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Рег.номер/филиал',
      dataIndex: 'COLUMN_17',
      key: 'COLUMN_17',
      columnType: 'java.lang.String',
      width: 179,
      align: 'left',
      columnUuid: '17',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'ОКАТО',
      dataIndex: 'COLUMN_18',
      key: 'COLUMN_18',
      columnType: 'java.lang.String',
      width: 179,
      align: 'left',
      columnUuid: '18',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Размер выделенной квоты',
      dataIndex: 'COLUMN_19',
      key: 'COLUMN_19',
      columnType: 'java.lang.String',
      width: 176,
      align: 'left',
      columnUuid: '19',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Код проверки',
      dataIndex: 'COLUMN_26',
      key: 'COLUMN_26',
      columnType: 'java.lang.String',
      width: 176,
      align: 'left',
      columnUuid: '26',
      filterType: null,
      filters: null,
      sortable: true,
    },
  ],
  rows: [
    {
      COLUMN_1: '181',
      COLUMN_10: '',
      COLUMN_11:
        'Общество с ограниченной ответственностью "Кетовский коммерческий банк"',
      COLUMN_13: '641310, Курганская область, с.Кетово, ул.Красина, 19',
      COLUMN_14: '641310, Курганская область, с.Кетово, ул.Красина, 19',
      COLUMN_15: '4510000735',
      COLUMN_16: '1024500000392',
      COLUMN_17: '842',
      COLUMN_18: '37',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '6i24091',
      COLUMN_3: 'Проверка сентябрь, 2020',
      COLUMN_4: '185',
      COLUMN_5: '6i - Уральский МЦИ',
      COLUMN_6: 'Уральский межрегиональный центр инспектирования (УМЦИ)',
      COLUMN_7: '01.09.2020',
      COLUMN_8: '9',
      COLUMN_9: '30.11.2018',
      key: '1',
      rowId: {
        auditCode: '1993e739-8ac6-4e62-a9f7-e9b3b3a19841',
        auditDate: '2020-09-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ООО КБ "Кетовский" (Рег. номер: 842 , ИНН: 4510000735)',
    },
    {
      COLUMN_1: '182',
      COLUMN_10: '31.07.2022',
      COLUMN_11: 'Неолант тенакс',
      COLUMN_13: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_14: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_15: '3906900937',
      COLUMN_16: '2222222222222',
      COLUMN_17: '',
      COLUMN_18: '45',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '',
      COLUMN_3: 'Проверка июль 2022 год',
      COLUMN_4: '184',
      COLUMN_5: '2i - ТУ БР (с 01.06.2023) / МЦИ КО по Центральному ФО',
      COLUMN_6:
        'Управление проверок № 1 Межрегионального центра инспектирования Центрального федерального округа (МЦИ ЦФО)',
      COLUMN_7: '01.07.2022',
      COLUMN_8: '7',
      COLUMN_9: '',
      key: '2',
      rowId: {
        auditCode: '77a9e0a8-8d77-4d11-9672-02eb07d15087',
        auditDate: '2022-07-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'Неолант тенакс (Рег. номер:  , ИНН: 3906900937)',
    },
    {
      COLUMN_1: '183',
      COLUMN_10: '30.06.2022',
      COLUMN_11: 'Неолант тенакс',
      COLUMN_13: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_14: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_15: '3906900937',
      COLUMN_16: '2222222222222',
      COLUMN_17: '',
      COLUMN_18: '45',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '2f069r1',
      COLUMN_3: 'Проверка июнь 2022 год',
      COLUMN_4: '183',
      COLUMN_5: '2i - ТУ БР (с 01.06.2023) / МЦИ КО по Центральному ФО',
      COLUMN_6:
        'Управление проверок № 1 Межрегионального центра инспектирования Центрального федерального округа (МЦИ ЦФО)',
      COLUMN_7: '01.06.2022',
      COLUMN_8: '6',
      COLUMN_9: '30.11.2020',
      key: '3',
      rowId: {
        auditCode: '1b795571-4b57-4ae4-9315-8ac64127e1ec',
        auditDate: '2022-06-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'Неолант тенакс (Рег. номер:  , ИНН: 3906900937)',
    },
    {
      COLUMN_1: '184',
      COLUMN_10: '31.05.2022',
      COLUMN_11: 'Неолант тенакс',
      COLUMN_13: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_14: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_15: '3906900937',
      COLUMN_16: '2222222222222',
      COLUMN_17: '',
      COLUMN_18: '45',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '',
      COLUMN_3: 'Проверка май 2022 год',
      COLUMN_4: '182',
      COLUMN_5: '2i - ТУ БР (с 01.06.2023) / МЦИ КО по Центральному ФО',
      COLUMN_6:
        'Управление проверок № 1 Межрегионального центра инспектирования Центрального федерального округа (МЦИ ЦФО)',
      COLUMN_7: '01.05.2022',
      COLUMN_8: '5',
      COLUMN_9: '',
      key: '4',
      rowId: {
        auditCode: 'ce0e2289-20fd-4cf4-9027-4ebaf8b0d27c',
        auditDate: '2022-05-01 00:00:00.0',
        canOpen: false,
      },
      tabTitle: 'Неолант тенакс (Рег. номер:  , ИНН: 3906900937)',
    },
    {
      COLUMN_1: '185',
      COLUMN_10: '30.04.2022',
      COLUMN_11: 'Неолант тенакс',
      COLUMN_13: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_14: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_15: '3906900937',
      COLUMN_16: '2222222222222',
      COLUMN_17: '',
      COLUMN_18: '45',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '',
      COLUMN_3: 'Проверка апрель 2022 год',
      COLUMN_4: '181',
      COLUMN_5: '2i - ТУ БР (с 01.06.2023) / МЦИ КО по Центральному ФО',
      COLUMN_6:
        'Управление проверок № 1 Межрегионального центра инспектирования Центрального федерального округа (МЦИ ЦФО)',
      COLUMN_7: '01.04.2022',
      COLUMN_8: '4',
      COLUMN_9: '',
      key: '5',
      rowId: {
        auditCode: '52994664-9b99-4859-872f-6fa36ab75cab',
        auditDate: '2022-04-01 00:00:00.0',
        canOpen: false,
      },
      tabTitle: 'Неолант тенакс (Рег. номер:  , ИНН: 3906900937)',
    },
    {
      COLUMN_1: '186',
      COLUMN_10: '31.03.2022',
      COLUMN_11: 'Неолант тенакс',
      COLUMN_13: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_14: '129075, г. Москва, Мурманский пр-д, 14 к. 1, тест',
      COLUMN_15: '3906900937',
      COLUMN_16: '2222222222222',
      COLUMN_17: '',
      COLUMN_18: '45',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '',
      COLUMN_3: 'Проверка март 2022 год',
      COLUMN_4: '180',
      COLUMN_5: '2i - ТУ БР (с 01.06.2023) / МЦИ КО по Центральному ФО',
      COLUMN_6:
        'Южный и Северо-Кавказский межрегиональный центр инспектирования (ЮСКМЦИ)',
      COLUMN_7: '01.03.2022',
      COLUMN_8: '3',
      COLUMN_9: '',
      key: '6',
      rowId: {
        auditCode: '1ac18ca1-9dc7-4c7e-980a-0736ca256aef',
        auditDate: '2022-03-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'Неолант тенакс (Рег. номер:  , ИНН: 3906900937)',
    },
    {
      COLUMN_1: '187',
      COLUMN_10: '',
      COLUMN_11: 'ПромТрансБанк (Общество с ограниченной ответственностью)',
      COLUMN_13: '450008 , Республика Башкортостан, г. Уфа, ул.Ленина, 70',
      COLUMN_14: '450008 , Республика Башкортостан, г. Уфа, ул.Ленина, 70',
      COLUMN_15: '0274045684',
      COLUMN_16: '1020200000083',
      COLUMN_17: '2638',
      COLUMN_18: '80',
      COLUMN_19: '0',
      COLUMN_2: 'Создан',
      COLUMN_26: '',
      COLUMN_3: 'Проверка ноябрь 2020 год',
      COLUMN_4: '179',
      COLUMN_5: '6i - Уральский МЦИ',
      COLUMN_6: 'Уральский межрегиональный центр инспектирования (УМЦИ)',
      COLUMN_7: '01.11.2020',
      COLUMN_8: '11',
      COLUMN_9: '25.01.2019',
      key: '7',
      rowId: {
        auditCode: '8d026d38-8ab7-4457-bffd-6e61601e8c1d',
        auditDate: '2020-11-01 00:00:00.0',
        canOpen: false,
      },
      tabTitle: 'Банк ПТБ (ООО) (Рег. номер: 2638 , ИНН: 0274045684)',
    },
    {
      COLUMN_1: '188',
      COLUMN_10: '',
      COLUMN_11: 'Публичное акционерное общество "НБД-Банк"',
      COLUMN_13: '603950, г.Нижний Новгород, площадь Горького, 6',
      COLUMN_14: '603950, г.Нижний Новгород, площадь Горького, 6',
      COLUMN_15: '5200000222',
      COLUMN_16: '1025200000022',
      COLUMN_17: '1966',
      COLUMN_18: '22',
      COLUMN_19: '0',
      COLUMN_2: 'Открыт',
      COLUMN_26: '',
      COLUMN_3: 'Проверка ноябрь 2020 год',
      COLUMN_4: '178',
      COLUMN_5: '5i - Волго-Вятский МЦИ',
      COLUMN_6:
        'Южный и Северо-Кавказский межрегиональный центр инспектирования (ЮСКМЦИ)',
      COLUMN_7: '01.11.2020',
      COLUMN_8: '11',
      COLUMN_9: '06.02.2019',
      key: '8',
      rowId: {
        auditCode: '37a189d5-d312-4b16-ad44-0addc01af4eb',
        auditDate: '2020-11-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ПАО "НБД-Банк" (Рег. номер: 1966 , ИНН: 5200000222)',
    },
    {
      COLUMN_1: '189',
      COLUMN_10: '',
      COLUMN_11:
        'Акционерный Коммерческий Банк "НОВИКОМБАНК" акционерное общество',
      COLUMN_13: '119180,  г. Москва, ул. Полянка Большая, д. 50/1, стр. 1',
      COLUMN_14: '119180,  г. Москва, ул. Полянка Большая, д. 50/1, стр. 1',
      COLUMN_15: '7706196340',
      COLUMN_16: '1027739075891',
      COLUMN_17: '2546',
      COLUMN_18: '45',
      COLUMN_19: '0',
      COLUMN_2: 'Закрыт',
      COLUMN_26: '',
      COLUMN_3: 'Проверка декабрь 2020 год',
      COLUMN_4: '177',
      COLUMN_5: '2i - ТУ БР (с 01.06.2023) / МЦИ КО по Центральному ФО',
      COLUMN_6: 'Северо-Западный межрегиональный центр инспектирования (СЗМЦИ)',
      COLUMN_7: '01.12.2020',
      COLUMN_8: '12',
      COLUMN_9: '03.04.2019',
      key: '9',
      rowId: {
        auditCode: '11d088e5-7194-46f2-9786-ae7d65fbea38',
        auditDate: '2020-12-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'АО АКБ "НОВИКОМБАНК" (Рег. номер: 2546 , ИНН: 7706196340)',
    },
    {
      COLUMN_1: '190',
      COLUMN_10: '',
      COLUMN_11:
        'АКЦИОНЕРНЫЙ КОММЕРЧЕСКИЙ БАНК "ЧЕЛИНДБАНК" (публичное акционерное общество)',
      COLUMN_13: '454091, г.Челябинск, ул.К.Маркса, 80',
      COLUMN_14: '454091, г.Челябинск, ул.К.Маркса, 80',
      COLUMN_15: '7453002182',
      COLUMN_16: '1027400000110',
      COLUMN_17: '485',
      COLUMN_18: '75',
      COLUMN_19: '0',
      COLUMN_2: 'Открыт',
      COLUMN_26: '',
      COLUMN_3: 'Проверка октябрь 2020 год',
      COLUMN_4: '176',
      COLUMN_5: '6i - Уральский МЦИ',
      COLUMN_6: 'Уральский межрегиональный центр инспектирования (УМЦИ)',
      COLUMN_7: '01.10.2020',
      COLUMN_8: '10',
      COLUMN_9: '15.02.2019',
      key: '10',
      rowId: {
        auditCode: 'aded24a3-265d-4349-9f01-7270ace2d907',
        auditDate: '2020-10-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ПАО "ЧЕЛИНДБАНК" (Рег. номер: 485 , ИНН: 7453002182)',
    },
  ],
  reportTitle: 'Кабинеты рабочих групп',
  pagination: {
    total: 194,
    pageSize: 0,
    gridId: '',
  },
  sort: {
    orders: [],
    sorted: false,
    unsorted: true,
    empty: true,
  },
  group: null,
};
