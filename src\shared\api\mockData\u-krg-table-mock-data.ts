export const uKrgTableMockData = {
  columns: [
    {
      title: '№',
      dataIndex: 'COLUMN_1',
      key: 'COLUMN_1',
      columnType: 'java.lang.Long',
      width: 64,
      align: 'left',
      columnUuid: '1',
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Статус-состояние КРГ',
      dataIndex: 'COLUMN_2',
      key: 'COLUMN_2',
      columnType: 'java.lang.String',
      width: 176,
      align: 'left',
      columnUuid: '2',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Текстовое описание КРГ',
      dataIndex: 'COLUMN_3',
      key: 'COLUMN_3',
      columnType: 'java.lang.String',
      width: 320,
      align: 'left',
      columnUuid: '3',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Код КРГ',
      dataIndex: 'COLUMN_4',
      key: 'COLUMN_4',
      columnType:
        'ru.lanit.fpsid.kzid_backend.result_sets.renderers.CustomizedString',
      width: 204,
      align: 'left',
      columnUuid: '4',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Дата начала проверки',
      dataIndex: 'COLUMN_5',
      key: 'COLUMN_5',
      columnType: 'java.sql.Timestamp',
      width: 256,
      align: 'left',
      columnUuid: '5',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Месяц начала проверки',
      dataIndex: 'COLUMN_6',
      key: 'COLUMN_6',
      columnType: 'java.lang.String',
      width: 112,
      align: 'left',
      columnUuid: '6',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Предполагаемая дата завершения проверки',
      dataIndex: 'COLUMN_7',
      key: 'COLUMN_7',
      columnType: 'java.sql.Timestamp',
      width: 256,
      align: 'left',
      columnUuid: '7',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Дата завершения проверки',
      dataIndex: 'COLUMN_8',
      key: 'COLUMN_8',
      columnType: 'java.sql.Timestamp',
      width: 256,
      align: 'left',
      columnUuid: '8',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Наименование ПЛ',
      dataIndex: 'COLUMN_9',
      key: 'COLUMN_9',
      columnType: 'java.lang.String',
      width: 768,
      align: 'left',
      columnUuid: '9',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Юридический адрес ПЛ',
      dataIndex: 'COLUMN_11',
      key: 'COLUMN_11',
      columnType: 'java.lang.String',
      width: 768,
      align: 'left',
      columnUuid: '11',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Фактический адрес ПЛ',
      dataIndex: 'COLUMN_12',
      key: 'COLUMN_12',
      columnType: 'java.lang.String',
      width: 768,
      align: 'left',
      columnUuid: '12',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'ИНН ПЛ',
      dataIndex: 'COLUMN_13',
      key: 'COLUMN_13',
      columnType: 'java.lang.String',
      width: 281,
      align: 'left',
      columnUuid: '13',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'ОГРН ПЛ',
      dataIndex: 'COLUMN_14',
      key: 'COLUMN_14',
      columnType: 'java.lang.String',
      width: 281,
      align: 'left',
      columnUuid: '14',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Рег.номер/филиал',
      dataIndex: 'COLUMN_15',
      key: 'COLUMN_15',
      columnType: 'java.lang.String',
      width: 179,
      align: 'left',
      columnUuid: '15',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'ОКАТО',
      dataIndex: 'COLUMN_16',
      key: 'COLUMN_16',
      columnType: 'java.lang.String',
      width: 179,
      align: 'left',
      columnUuid: '16',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Размер выделенной квоты',
      dataIndex: 'COLUMN_17',
      key: 'COLUMN_17',
      columnType: 'java.lang.String',
      width: 176,
      align: 'left',
      columnUuid: '17',
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Дата создания КРГ',
      dataIndex: 'COLUMN_18',
      key: 'COLUMN_18',
      columnType: 'java.sql.Timestamp',
      width: 176,
      align: 'left',
      columnUuid: '18',
      filterType: null,
      filters: null,
      sortable: true,
    },
    {
      title: 'Режим активации',
      dataIndex: 'COLUMN_19',
      key: 'COLUMN_19',
      columnType: 'java.lang.String',
      width: 176,
      align: 'left',
      columnUuid: '19',
      filterType: null,
      filters: null,
      sortable: false,
    },
    {
      title: 'Комментарий',
      dataIndex: 'COLUMN_25',
      key: 'COLUMN_25',
      columnType: 'java.lang.String',
      width: 256,
      align: 'left',
      columnUuid: '25',
      filterType: null,
      filters: null,
      sortable: true,
    },
  ],
  rows: [
    {
      COLUMN_1: '1',
      COLUMN_11:
        '664047, Иркутская область, г. Иркутск, ул. Карла Либкнехта, д. 121, оф. 507',
      COLUMN_12:
        '664047, Иркутская область, г. Иркутск, ул. Карла Либкнехта, д. 121, оф. 507',
      COLUMN_13: '5407264020',
      COLUMN_14: '1155476135110',
      COLUMN_15: '',
      COLUMN_16: '25',
      COLUMN_17: '0',
      COLUMN_18: '20.05.2025 11:45:02',
      COLUMN_19: 'автоматический',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 07 2020год',
      COLUMN_4: '342',
      COLUMN_5: '01.07.2020',
      COLUMN_6: '7',
      COLUMN_7: '15.08.2019',
      COLUMN_8: '-',
      COLUMN_9:
        'Общество с ограниченной ответственностью Микрофинансовая компания «КОНГА»',
      key: '1',
      rowId: {
        auditCode: 'adb2f594-8c6c-498a-ba12-ea2a9ce17952',
        auditDate: '2020-07-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ООО МикроФК "КОНГА"',
    },
    {
      COLUMN_1: '2',
      COLUMN_11: '115114,  г. Москва, 2-й Кожевнический переулок, д. 7',
      COLUMN_12: '115114,  г. Москва, 2-й Кожевнический переулок, д. 7',
      COLUMN_13: '7703033450',
      COLUMN_14: '1027739045124',
      COLUMN_15: '3247',
      COLUMN_16: '45',
      COLUMN_17: '0',
      COLUMN_18: '19.05.2025 13:30:31',
      COLUMN_19: 'ручной',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 12 2020год',
      COLUMN_4: '341',
      COLUMN_5: '01.12.2020',
      COLUMN_6: '12',
      COLUMN_7: '',
      COLUMN_8: '',
      COLUMN_9:
        'АКЦИОНЕРНОЕ ОБЩЕСТВО АКЦИОНЕРНЫЙ ИНВЕСТИЦИОННЫЙ БАНК МОСКОВСКОГО МЕЖДУНАРОДНОГО ДЕЛОВОГО ЦЕНТРА "МОСКВА-СИТИ"',
      key: '2',
      rowId: {
        auditCode: '8cd65814-b202-4775-9ad6-bcf587f24d5b',
        auditDate: '2020-12-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'АО БАНК "МОСКВА-СИТИ"',
    },
    {
      COLUMN_1: '3',
      COLUMN_11: '105082, г. Москва, Переведеновский пер., д. 13, стр. 4',
      COLUMN_12: '105082, г. Москва, Переведеновский пер., д. 13, стр. 4',
      COLUMN_13: '7729399756',
      COLUMN_14: '1027739051757',
      COLUMN_15: '3351',
      COLUMN_16: '45',
      COLUMN_17: '0',
      COLUMN_18: '19.05.2025 10:18:25',
      COLUMN_19: 'автоматический',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 07 2020год',
      COLUMN_4: '340',
      COLUMN_5: '01.07.2020',
      COLUMN_6: '7',
      COLUMN_7: '08.08.2018',
      COLUMN_8: '',
      COLUMN_9: 'Акционерное общество Банк "Резервные финансы и инвестиции"',
      key: '3',
      rowId: {
        auditCode: '15bf1ad9-7a90-4bba-bc2e-5475203efc95',
        auditDate: '2020-07-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'АО "РФИ БАНК"',
    },
    {
      COLUMN_1: '4',
      COLUMN_11:
        '129110, ГОРОД. МОСКВА, ПРОСПЕКТ. МИРА, ДОМ 69, СТРОЕНИЕ 1, ЭТАЖ 5 ПОМЕЩ. XI КОМН. 62',
      COLUMN_12:
        '129110, ГОРОД. МОСКВА, ПРОСПЕКТ. МИРА, ДОМ 69, СТРОЕНИЕ 1, ЭТАЖ 5 ПОМЕЩ. XI КОМН. 62',
      COLUMN_13: '5407257111',
      COLUMN_14: '1035403212646',
      COLUMN_15: '',
      COLUMN_16: '45',
      COLUMN_17: '0',
      COLUMN_18: '16.05.2025 17:14:44',
      COLUMN_19: 'ручной',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 08 2020год',
      COLUMN_4: '339',
      COLUMN_5: '01.08.2020',
      COLUMN_6: '8',
      COLUMN_7: '',
      COLUMN_8: '',
      COLUMN_9:
        'Общество с ограниченной ответственностью "Межрегиональный специализированный депозитарий"',
      key: '4',
      rowId: {
        auditCode: '64c432c4-5073-4f67-8662-0e443a2c7a4f',
        auditDate: '2020-08-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle:
        'НОВОСИБИРСКИЙ ФИЛИАЛ ОБЩЕСТВА С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "МЕЖРЕГИОНАЛЬНЫЙ СПЕЦИАЛИЗИРОВАННЫЙ ДЕПОЗИТАРИЙ"',
    },
    {
      COLUMN_1: '5',
      COLUMN_11:
        '129110, г. Москва, Проспект Мира, д. 69, стр. 1, эт. 5, помещ. XI, комн. 62',
      COLUMN_12: '630099, г. Новосибирск,  ул. Советская, 37, 7 этаж',
      COLUMN_13: '5407257111',
      COLUMN_14: '1035403212646',
      COLUMN_15: '',
      COLUMN_16: '45',
      COLUMN_17: '0',
      COLUMN_18: '13.05.2025 16:05:33',
      COLUMN_19: 'ручной',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 08 2020год',
      COLUMN_4: '338',
      COLUMN_5: '01.08.2020',
      COLUMN_6: '8',
      COLUMN_7: '28.09.2018',
      COLUMN_8: '',
      COLUMN_9:
        'Общество с ограниченной ответственностью "Межрегиональный специализированный депозитарий"',
      key: '5',
      rowId: {
        auditCode: 'c672f810-3cd4-44b3-a983-e3e6e8d6489f',
        auditDate: '2020-08-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ООО "Межрегиональный СД"',
    },
    {
      COLUMN_1: '6',
      COLUMN_11:
        '650000, ОБЛАСТЬ КЕМЕРОВСКАЯ, ГОРОД КЕМЕРОВО, УЛИЦА ВЕСЕННЯЯ, 5',
      COLUMN_12:
        '650000, ОБЛАСТЬ КЕМЕРОВСКАЯ, ГОРОД КЕМЕРОВО, УЛИЦА ВЕСЕННЯЯ, 5',
      COLUMN_13: '4205002133',
      COLUMN_14: '1024200687280',
      COLUMN_15: '',
      COLUMN_16: '32',
      COLUMN_17: '0',
      COLUMN_18: '12.05.2025 13:48:09',
      COLUMN_19: 'ручной',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 11 2020год',
      COLUMN_4: '337',
      COLUMN_5: '01.11.2020',
      COLUMN_6: '11',
      COLUMN_7: '08.02.2019',
      COLUMN_8: '',
      COLUMN_9:
        'Общество с ограниченной ответственностью "Страховая компания "Сибирский Дом Страхования"',
      key: '6',
      rowId: {
        auditCode: '4c40bc78-d26d-46ab-8275-a4b9517ed7e9',
        auditDate: '2020-11-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ООО "СК "СИБИРСКИЙ ДОМ СТРАХОВАНИЯ"',
    },
    {
      COLUMN_1: '7',
      COLUMN_11:
        '141802, ОБЛАСТЬ МОСКОВСКАЯ, РАЙОН ДМИТРОВСКИЙ, ГОРОД ДМИТРОВ, УЛИЦА БИРЛОВО ПОЛЕ, ДОМ 13, ОФИС 3',
      COLUMN_12: '109147, ГОРОД МОСКВА, УЛИЦА ТАГАНСКАЯ, ДОМ 17-23',
      COLUMN_13: '7733108576',
      COLUMN_14: '1027739045520',
      COLUMN_15: '',
      COLUMN_16: '45',
      COLUMN_17: '0',
      COLUMN_18: '11.05.2025 20:30:01',
      COLUMN_19: 'автоматический',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 08 2020год',
      COLUMN_4: '336',
      COLUMN_5: '01.08.2020',
      COLUMN_6: '8',
      COLUMN_7: '05.11.2019',
      COLUMN_8: '-',
      COLUMN_9: 'Общество с ограниченной ответственностью "ИНКОР Страхование"',
      key: '7',
      rowId: {
        auditCode: '5263091d-b86c-4d30-9284-37ec767cc009',
        auditDate: '2020-08-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'ОБЩЕСТВО С ОГРАНИЧЕННОЙ ОТВЕТСТВЕННОСТЬЮ "ИНКОР СТРАХОВАНИЕ" ',
    },
    {
      COLUMN_1: '8',
      COLUMN_11: '426076, Удмуртская Республика, г.Ижевск, ул. Ленина, 30',
      COLUMN_12: '426076, Удмуртская Республика, г.Ижевск, ул. Ленина, 30',
      COLUMN_13: '1835047032',
      COLUMN_14: '1021800000090',
      COLUMN_15: '646',
      COLUMN_16: '94',
      COLUMN_17: '0',
      COLUMN_18: '11.05.2025 20:18:14',
      COLUMN_19: 'ручной',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 12 2020год',
      COLUMN_4: '335',
      COLUMN_5: '01.12.2020',
      COLUMN_6: '12',
      COLUMN_7: '',
      COLUMN_8: '',
      COLUMN_9:
        'Акционерный коммерческий банк "Ижкомбанк" (публичное акционерное общество)',
      key: '8',
      rowId: {
        auditCode: 'f9112039-da95-442d-b924-08aa9dc46a1f',
        auditDate: '2020-12-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'АКБ "Ижкомбанк" (ПАО)',
    },
    {
      COLUMN_1: '9',
      COLUMN_11: '107996, г. Москва, ул. Рождественка, д. 8, стр. 1',
      COLUMN_12: '107996, г. Москва, ул. Рождественка, д. 8, стр. 1',
      COLUMN_13: '7702000406',
      COLUMN_14: '1027700159497',
      COLUMN_15: '2748',
      COLUMN_16: '45',
      COLUMN_17: '0',
      COLUMN_18: '11.05.2025 18:15:43',
      COLUMN_19: 'автоматический',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 07 2020год',
      COLUMN_4: '334',
      COLUMN_5: '01.07.2020',
      COLUMN_6: '7',
      COLUMN_7: '03.09.2019',
      COLUMN_8: '',
      COLUMN_9: 'Акционерное общество "БМ-Банк"',
      key: '9',
      rowId: {
        auditCode: '994bdd55-4c79-4137-9a63-33f7d60aa1b4',
        auditDate: '2020-07-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'АО "БМ-Банк"',
    },
    {
      COLUMN_1: '10',
      COLUMN_11:
        '658200, Алтайский край, город Рубцовск, улица Куйбышева, дом 54',
      COLUMN_12:
        '658200, Алтайский край, город Рубцовск, улица Куйбышева, дом 54',
      COLUMN_13: '2209023910',
      COLUMN_14: '1022200815812',
      COLUMN_15: '',
      COLUMN_16: '01',
      COLUMN_17: '0',
      COLUMN_18: '11.05.2025 17:59:47',
      COLUMN_19: 'ручной',
      COLUMN_2: 'Создан',
      COLUMN_25: '',
      COLUMN_3: 'Проверка 08 2020год',
      COLUMN_4: '333',
      COLUMN_5: '01.08.2020',
      COLUMN_6: '8',
      COLUMN_7: '26.09.2019',
      COLUMN_8: '-',
      COLUMN_9: 'КРЕДИТНЫЙ ПОТРЕБИТЕЛЬСКИЙ КООПЕРАТИВ "РЕЗЕРВ"',
      key: '10',
      rowId: {
        auditCode: '057b69bb-be6f-48e1-8e56-1cf8a242c50e',
        auditDate: '2020-08-01 00:00:00.0',
        canOpen: true,
      },
      tabTitle: 'КПК "РЕЗЕРВ"',
    },
  ],
  reportTitle: 'Управление кабинетами рабочих групп',
  pagination: {
    total: 165,
    pageSize: 0,
    gridId: '',
  },
  sort: {
    orders: [],
    unsorted: true,
    sorted: false,
    empty: true,
  },
  group: null,
};
