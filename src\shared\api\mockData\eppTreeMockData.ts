export const eppTreeMockData = {
  treeData: [
    {
      title: 'Материалы',
      rawTitle: 'Материалы',
      key: '0-0-0',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: '7a7efe90-4e26-48d8-84e6-2768a818a29c',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      position: 1,
      disabled: true,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 0,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Ведомости',
      rawTitle: 'Ведомости',
      key: '0-0-1',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 0,
      itemId: '6ee91d39-f140-4c07-89eb-477c8be94059',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      position: 2,
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 0,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Реестры',
      rawTitle: 'Реестры',
      key: '0-0-2',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 0,
      itemId: '389c9fe9-b118-4773-8e2a-327a183a4763',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      position: 3,
      disabled: false,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 15,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Результаты',
      rawTitle: 'Результаты',
      key: '0-0-3',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 0,
      itemId: '1af7bc7c-57e7-4d69-992b-16f635a0fe02',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      position: 4,
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 1,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: '10Mb.txt',
      rawTitle: '10Mb.txt',
      key: '0-0-4',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'b56133e1-7f7a-4fa3-953d-8eba4c366921',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'b56133e1-7f7a-4fa3-953d-8eba4c366921',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '10_Mb.txt',
      rawTitle: '10_Mb.txt',
      key: '0-0-5',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '37eb686e-c77a-47f1-9518-08d44dbb03b4',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '37eb686e-c77a-47f1-9518-08d44dbb03b4',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '111 (1) (1) (23187).txt',
      rawTitle: '111 (1) (1) (23187).txt',
      key: '0-0-6',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '59a03f15-a378-4751-ac6d-fffd85d84589',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '59a03f15-a378-4751-ac6d-fffd85d84589',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '111 (1) (1) (23188).txt',
      rawTitle: '111 (1) (1) (23188).txt',
      key: '0-0-7',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '8787b92a-500c-42e7-bfe9-0b9bf0a26e6f',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '8787b92a-500c-42e7-bfe9-0b9bf0a26e6f',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '111 (1).txt',
      rawTitle: '111 (1).txt',
      key: '0-0-8',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '9ee52806-7552-4c59-a2fc-348e104bc115',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '9ee52806-7552-4c59-a2fc-348e104bc115',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '666 (1) (1).txt',
      rawTitle: '666 (1) (1).txt',
      key: '0-0-9',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '87297c95-229a-4ba0-92ed-c27d69bdceb9',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '87297c95-229a-4ba0-92ed-c27d69bdceb9',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '666 (1) (2).txt',
      rawTitle: '666 (1) (2).txt',
      key: '0-0-10',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'd8c13e80-3452-4cf0-9df8-97047a7a1851',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'd8c13e80-3452-4cf0-9df8-97047a7a1851',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: '888 (2) (1).txt',
      rawTitle: '888 (2) (1).txt',
      key: '0-0-11',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '530f05a4-0457-4165-b929-e52c85bd57ee',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '530f05a4-0457-4165-b929-e52c85bd57ee',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'DocViewer_log.txt',
      rawTitle: 'DocViewer_log.txt',
      key: '0-0-12',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'a1915793-d4bc-4c08-b17a-576043bbbcfc',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'a1915793-d4bc-4c08-b17a-576043bbbcfc',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title:
        '_c2Fwb3pobmlrb3ZhZUB2aXAuY2JyLnJ1_c2Fwb3pobmlrb3ZhZUB2aXAuY2JyLnJ1__PPOD__search12_1682672392_8374.xml',
      rawTitle:
        '_c2Fwb3pobmlrb3ZhZUB2aXAuY2JyLnJ1_c2Fwb3pobmlrb3ZhZUB2aXAuY2JyLnJ1__PPOD__search12_1682672392_8374.xml',
      key: '0-0-13',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'd00aa8be-6535-47dc-9dd7-2d7547fbc5d3',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'd00aa8be-6535-47dc-9dd7-2d7547fbc5d3',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'middle (24279).txt',
      rawTitle: 'middle (24279).txt',
      key: '0-0-14',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'e98a48fe-7ee1-44ee-8d8b-8aefdcb2e36c',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'e98a48fe-7ee1-44ee-8d8b-8aefdcb2e36c',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'middle (24286).txt',
      rawTitle: 'middle (24286).txt',
      key: '0-0-15',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'e5b1b47c-f473-4bab-80d3-249a81ad10d4',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'e5b1b47c-f473-4bab-80d3-249a81ad10d4',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'middle (24305).txt',
      rawTitle: 'middle (24305).txt',
      key: '0-0-16',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '1d4dacaf-8895-44a0-bc06-687767cbd13d',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '1d4dacaf-8895-44a0-bc06-687767cbd13d',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'small.txt',
      rawTitle: 'small.txt',
      key: '0-0-17',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'ffc4f3f5-a800-4b27-b821-f6740d6ae9c0',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'ffc4f3f5-a800-4b27-b821-f6740d6ae9c0',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'test_2023_Jan_30.jpg',
      rawTitle: 'test_2023_Jan_30.jpg',
      key: '0-0-18',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'aa836c0e-5060-4755-9bed-8190aee2dc27',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'aa836c0e-5060-4755-9bed-8190aee2dc27',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'xxx (25748).txt',
      rawTitle: 'xxx (25748).txt',
      key: '0-0-19',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '407deee9-aad7-4a17-b9bb-df28fdebfa8a',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '407deee9-aad7-4a17-b9bb-df28fdebfa8a',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'xxx (25749).txt',
      rawTitle: 'xxx (25749).txt',
      key: '0-0-20',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'aa266a95-e303-4828-962c-df6620e6e421',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'aa266a95-e303-4828-962c-df6620e6e421',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'xxx (25750).txt',
      rawTitle: 'xxx (25750).txt',
      key: '0-0-21',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'ed1d1b07-62a5-421f-bf68-c9dd9e94f367',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'ed1d1b07-62a5-421f-bf68-c9dd9e94f367',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'ИЗИО',
      rawTitle: 'ИЗИО',
      key: '0-0-22',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: '7f9666ce-eaf3-457d-8f3d-c4154a8b30f3',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: true,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 1,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Руководство по интеграции 2.10 (24304).pdf',
      rawTitle: 'Руководство по интеграции 2.10 (24304).pdf',
      key: '0-0-23',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'fca5a86c-0cf3-4ab3-85c5-d542bfe24488',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'fca5a86c-0cf3-4ab3-85c5-d542bfe24488',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'Руководство по интеграции 2.10 (24339).pdf',
      rawTitle: 'Руководство по интеграции 2.10 (24339).pdf',
      key: '0-0-24',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '4b53c4a8-7a0b-49b3-a67e-00319b995226',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '4b53c4a8-7a0b-49b3-a67e-00319b995226',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'Руководство по интеграции 2.10 (24340).pdf',
      rawTitle: 'Руководство по интеграции 2.10 (24340).pdf',
      key: '0-0-25',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'b3a3913e-49d2-42a1-9be7-0aa328174d95',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'b3a3913e-49d2-42a1-9be7-0aa328174d95',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'Руководство по интеграции 2.10 (24341).pdf',
      rawTitle: 'Руководство по интеграции 2.10 (24341).pdf',
      key: '0-0-26',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: '7edcafca-7ef2-4018-99bc-30cfcd557e44',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: '7edcafca-7ef2-4018-99bc-30cfcd557e44',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
    {
      title: 'Тестовый каталог [i]',
      rawTitle: 'Тестовый каталог',
      key: '0-0-27',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 0,
      itemId: 'fbf47837-8c21-45dd-b2d0-769318f6e561',
      parent: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: 'i',
      linked: [],
      children: [],
      totalCountOfLeafs: 3,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
  ],
  history: [],
  foundNodes: [],
};

export const krgTreeRootMockData = {
  treeData: [
    {
      title: 'INNddmmk',
      rawTitle: 'INNddmmk',
      key: '0-0',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      position: 1,
      disabled: true,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 42,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
  ],
  history: [],
  foundNodes: [],
};

export const reestry = {
  treeData: [
    {
      title: 'Заявка 1',
      rawTitle: 'Заявка 1',
      key: '0-0-2-0',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: '20437bb6-9df0-41f1-962a-37c514acd9ac',
      parent: '389c9fe9-b118-4773-8e2a-327a183a4763',
      position: 1,
      disabled: true,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 0,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Заявка 2',
      rawTitle: 'Заявка 2',
      key: '0-0-2-1',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: '91389f57-4cbf-486e-8d2a-d9ecb4743531',
      parent: '389c9fe9-b118-4773-8e2a-327a183a4763',
      position: 2,
      disabled: true,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 0,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Заявка 4',
      rawTitle: 'Заявка 4',
      key: '0-0-2-2',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: 'fa657ffc-a379-46b6-b529-d0de17891dc2',
      parent: '389c9fe9-b118-4773-8e2a-327a183a4763',
      position: 3,
      disabled: true,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 4,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'Заявки 01-01',
      rawTitle: 'Заявки 01-01',
      key: '0-0-2-3',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 0,
      itemId: '6e53ead7-a76e-4759-8bd5-a7cc2def72fb',
      parent: '389c9fe9-b118-4773-8e2a-327a183a4763',
      position: 4,
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 11,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
  ],
  history: [],
  foundNodes: [],
};

export const resultaty = {
  treeData: [
    {
      title: 'Заявки 02_01',
      rawTitle: 'Заявки 02_01',
      key: '0-0-3-0',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 0,
      itemId: 'ae1a9df7-1125-4c5a-8edc-821befa97580',
      parent: '1af7bc7c-57e7-4d69-992b-16f635a0fe02',
      position: 1,
      disabled: false,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 0,
      hasLinked: false,
      isLeaf: true,
      isDirectory: true,
      isCallable: false,
    },
    {
      title: 'rr.txt',
      rawTitle: 'rr.txt',
      key: '0-0-3-1',
      color: 'rgba(0,0,255,0.5)',
      isMain: 0,
      itemId: 'b4778183-0816-4574-ae68-20f3ab6e7297',
      parent: '1af7bc7c-57e7-4d69-992b-16f635a0fe02',
      disabled: false,
      checked: false,
      cabinetOnly: true,
      tagged: false,
      marked: '',
      fileNetId: 'b4778183-0816-4574-ae68-20f3ab6e7297',
      fileSignExist: false,
      linked: [],
      children: [],
      isLeaf: true,
      isDirectory: false,
      isCallable: false,
      permissions: {
        canView: true,
        canDownload: true,
        canPrint: true,
      },
      isRemovable: true,
    },
  ],
  history: [],
  foundNodes: [],
};

export const rootTreeMockData = {
  treeData: [
    {
      title: 'INNddmmk',
      rawTitle: 'INNddmmk',
      key: '0-0',
      color: 'rgba(0, 0, 0, 0.65)',
      isFixed: 1,
      itemId: '4109a8e1-7b82-44b5-b33a-ee978c425517',
      position: 1,
      disabled: true,
      checked: false,
      cabinetOnly: false,
      tagged: false,
      marked: '',
      linked: [],
      children: [],
      totalCountOfLeafs: 42,
      hasLinked: false,
      isLeaf: false,
      isDirectory: true,
      isCallable: false,
    },
  ],
  history: [],
  foundNodes: [],
};
